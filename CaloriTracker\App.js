import { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider as PaperProvider } from 'react-native-paper';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import AppNavigator from './src/navigation/AppNavigator';
import DataService from './src/services/dataService';
import { theme } from './src/theme/theme';

export default function App() {
  useEffect(() => {
    // Initialize data service when app starts
    const initializeApp = async () => {
      try {
        await DataService.initialize();
        console.log('App initialized successfully');
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={theme}>
        <AppNavigator />
        <StatusBar style="light" backgroundColor={theme.colors.primary} />
      </PaperProvider>
    </GestureHandlerRootView>
  );
}
