{"version": 3, "file": "ExpoPermissions.web.js", "sourceRoot": "", "sources": ["../src/ExpoPermissions.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAGL,gBAAgB,GAEjB,MAAM,qBAAqB,CAAC;AAE7B;;;GAGG;AAEH,iHAAiH;AACjH,0FAA0F;AAC1F,SAAS,aAAa,CAAC,WAAmC;IACxD,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE;QACjE,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KACzD;IAED,iFAAiF;IACjF,+DAA+D;IAC/D,oEAAoE;IAEpE,yDAAyD;IACzD,MAAM,YAAY;IAChB,yHAAyH;IACzH,SAAS,CAAC,YAAY;QACtB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,eAAe;QACzB;YACE,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACzD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;YACf,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;IAEJ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,0BAA0B,CACvC,OAA+B;IAE/B,IAAI;QACF,MAAM,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACpB,wBAAwB;QACxB,UAAU;QACV,IAAI,OAAO,KAAK,sBAAsB,EAAE;YACtC,gCAAgC;YAChC,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;gBACrC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;SACH;aAAM;YACL,6DAA6D;YAC7D,gDAAgD;YAChD,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;SACH;KACF;AACH,CAAC;AAED,KAAK,UAAU,+BAA+B;IAC5C,OAAO,MAAM,0BAA0B,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,KAAK,UAAU,2BAA2B;IACxC,OAAO,MAAM,0BAA0B,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,KAAK,UAAU,6BAA6B;IAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,SAAS,CAAC,WAAW,CAAC,kBAAkB,CACtC,GAAG,EAAE,CACH,OAAO,CAAC;YACN,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC,EACJ,CAAC,EAAE,IAAI,EAA4B,EAAE,EAAE;YACrC,sEAAsE;YACtE,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,OAAO,CAAC;oBACN,MAAM,EAAE,gBAAgB,CAAC,MAAM;oBAC/B,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC;oBACN,MAAM,EAAE,gBAAgB,CAAC,YAAY;oBACrC,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;aACJ;QACH,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,2BAA2B,CACxC,IAAwC;IAExC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK;QAAE,OAAO,IAAI,CAAC;IAEtF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9D,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,OAAO,gBAAgB,CAAC,YAAY,CAAC;KACtC;SAAM,IAAI,KAAK,KAAK,SAAS,EAAE;QAC9B,OAAO,gBAAgB,CAAC,OAAO,CAAC;KACjC;SAAM,IAAI,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,gBAAgB,CAAC,MAAM,CAAC;KAChC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,gBAAgB;IAC7B,IAAI,SAAS,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE;QAClF,OAAO,MAAM,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;KACxD;IAED,wEAAwE;IACxE,IAAI,MAAM,CAAC,gBAAgB,IAAI,OAAO,MAAM,CAAC,gBAAgB,CAAC,UAAU,KAAK,UAAU,EAAE;QACvF,aAAa;QACb,OAAO,MAAM,gBAAgB,CAAC,UAAU,EAAE,CAAC;KAC5C;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,wBAAwB;IACrC,MAAM,iBAAiB,GAAG,0BAA0B,EAAE,CAAC;IACvD,gFAAgF;IAChF,kDAAkD;IAClD,IAAI,CAAC,iBAAiB;QAAE,OAAO,gBAAgB,CAAC,OAAO,CAAC;IAExD,iEAAiE;IACjE,0GAA0G;IAC1G,MAAM,MAAM,GAAG,MAAM,iBAAiB,EAAE,CAAC;IACzC,QAAQ,MAAM,EAAE;QACd,KAAK,SAAS;YACZ,OAAO,gBAAgB,CAAC,OAAO,CAAC;QAClC,KAAK,QAAQ;YACX,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACjC;YACE,OAAO,gBAAgB,CAAC,YAAY,CAAC;KACxC;AACH,CAAC;AAED,KAAK,UAAU,yBAAyB,CAAC,UAA2B;IAClE,MAAM,OAAO,GAAG,MAAM,gBAAgB,EAAE,CAAC;IACzC,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,KAAK,CAAC;KACd;IACD,MAAM,MAAM,GAAG,MAAM,OAAO;SACzB,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC;SACzC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;IACrC,kDAAkD;IAClD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,UAA0B,EAC1B,SAAkB;IAElB,QAAQ,UAAU,EAAE;QAClB,KAAK,yBAAyB,CAAC;QAC/B,KAAK,eAAe;YAClB;gBACE,IAAI,CAAC,SAAS,EAAE;oBACd,MAAM,MAAM,GAAG,MAAM,2BAA2B,CAAC,eAAe,CAAC,CAAC;oBAClE,IAAI,MAAM,EAAE;wBACV,OAAO;4BACL,MAAM;4BACN,OAAO,EAAE,OAAO;4BAChB,OAAO,EAAE,MAAM,KAAK,gBAAgB,CAAC,OAAO;4BAC5C,WAAW,EAAE,IAAI;yBAClB,CAAC;qBACH;iBACF;gBAED,MAAM,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,MAAa,CAAC;gBAC5C,IAAI,YAAY,CAAC,iBAAiB,EAAE;oBAClC,IAAI,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;oBACrC,IAAI,SAAS,EAAE;wBACb,MAAM,GAAG,MAAM,YAAY,CAAC,iBAAiB,EAAE,CAAC;qBACjD;oBACD,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE;wBACnC,OAAO;4BACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;4BACrC,OAAO,EAAE,OAAO;4BAChB,WAAW,EAAE,IAAI;4BACjB,OAAO,EAAE,KAAK;yBACf,CAAC;qBACH;oBACD,OAAO;wBACL,MAAM;wBACN,OAAO,EAAE,OAAO;wBAChB,WAAW,EAAE,IAAI;wBACjB,OAAO,EAAE,MAAM,KAAK,gBAAgB,CAAC,OAAO;qBAC7C,CAAC;iBACH;aACF;YACD,MAAM;QACR,KAAK,QAAQ,CAAC,CAAC;YACb,IAAI,SAAS,EAAE;gBACb,MAAM,MAAM,GAAG,MAAM,wBAAwB,EAAE,CAAC;gBAChD,OAAO;oBACL,MAAM;oBACN,OAAO,EAAE,OAAO;oBAChB,OAAO,EAAE,MAAM,KAAK,gBAAgB,CAAC,OAAO;oBAC5C,WAAW,EAAE,KAAK;iBACnB,CAAC;aACH;YAED,+DAA+D;YAC/D,MAAM,MAAM,GAAG,0BAA0B,EAAE;gBACzC,CAAC,CAAC,gBAAgB,CAAC,YAAY;gBAC/B,CAAC,CAAC,KAAK,EAAE;oBACT,CAAC,CAAC,gBAAgB,CAAC,MAAM;oBACzB,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC7B,OAAO;gBACL,MAAM;gBACN,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,MAAM,KAAK,gBAAgB,CAAC,OAAO;aAC7C,CAAC;SACH;QACD,KAAK,UAAU,CAAC;QAChB,KAAK,oBAAoB,CAAC;QAC1B,KAAK,oBAAoB;YACvB;gBACE,MAAM,WAAW,GAAG,MAAM,2BAA2B,CAAC,aAAa,CAAC,CAAC;gBACrE,IAAI,WAAW,EAAE;oBACf,IAAI,WAAW,KAAK,gBAAgB,CAAC,YAAY,IAAI,SAAS,EAAE;wBAC9D,OAAO,MAAM,6BAA6B,EAAE,CAAC;qBAC9C;oBACD,OAAO;wBACL,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,OAAO;wBAChB,WAAW,EAAE,IAAI;wBACjB,OAAO,EAAE,WAAW,KAAK,gBAAgB,CAAC,OAAO;qBAClD,CAAC;iBACH;qBAAM,IAAI,SAAS,EAAE;oBACpB,qEAAqE;oBACrE,OAAO,MAAM,6BAA6B,EAAE,CAAC;iBAC9C;aACF;YACD,MAAM;QACR,KAAK,gBAAgB;YACnB;gBACE,MAAM,WAAW,GAAG,MAAM,2BAA2B,CAAC,YAAY,CAAC,CAAC;gBACpE,IAAI,WAAW,EAAE;oBACf,IAAI,WAAW,KAAK,gBAAgB,CAAC,YAAY,IAAI,SAAS,EAAE;wBAC9D,OAAO,MAAM,+BAA+B,EAAE,CAAC;qBAChD;oBACD,OAAO;wBACL,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,OAAO;wBAChB,WAAW,EAAE,IAAI;wBACjB,OAAO,EAAE,WAAW,KAAK,gBAAgB,CAAC,OAAO;qBAClD,CAAC;iBACH;qBAAM,IAAI,SAAS,EAAE;oBACpB,OAAO,MAAM,+BAA+B,EAAE,CAAC;iBAChD;qBAAM;oBACL,MAAM,YAAY,GAAG,MAAM,yBAAyB,CAAC,YAAY,CAAC,CAAC;oBACnE,IAAI,YAAY,EAAE;wBAChB,OAAO;4BACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;4BAChC,OAAO,EAAE,OAAO;4BAChB,WAAW,EAAE,IAAI;4BACjB,OAAO,EAAE,IAAI;yBACd,CAAC;qBACH;oBACD,6CAA6C;iBAC9C;aACF;YACD,MAAM;QACR,KAAK,QAAQ;YACX;gBACE,MAAM,WAAW,GAAG,MAAM,2BAA2B,CAAC,QAAQ,CAAC,CAAC;gBAChE,IAAI,WAAW,EAAE;oBACf,IAAI,WAAW,KAAK,gBAAgB,CAAC,YAAY,IAAI,SAAS,EAAE;wBAC9D,OAAO,MAAM,2BAA2B,EAAE,CAAC;qBAC5C;oBACD,OAAO;wBACL,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,OAAO;wBAChB,WAAW,EAAE,IAAI;wBACjB,OAAO,EAAE,WAAW,KAAK,gBAAgB,CAAC,OAAO;qBAClD,CAAC;iBACH;qBAAM,IAAI,SAAS,EAAE;oBACpB,OAAO,MAAM,2BAA2B,EAAE,CAAC;iBAC5C;qBAAM;oBACL,MAAM,YAAY,GAAG,MAAM,yBAAyB,CAAC,YAAY,CAAC,CAAC;oBACnE,IAAI,YAAY,EAAE;wBAChB,OAAO;4BACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;4BAChC,OAAO,EAAE,OAAO;4BAChB,WAAW,EAAE,IAAI;4BACjB,OAAO,EAAE,IAAI;yBACd,CAAC;qBACH;oBACD,6CAA6C;iBAC9C;aACF;YACD,MAAM;QACR;YACE,MAAM;KACT;IACD,OAAO;QACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;QACrC,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,KAAK;KACf,CAAC;AACJ,CAAC;AAED,eAAe;IACb,IAAI,IAAI;QACN,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,eAAiC;QAC9C,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,MAAM,cAAc,IAAI,IAAI,GAAG,CAAC,eAAe,CAAC,EAAE;YACrD,OAAO,CAAC,cAAc,CAAC,GAAG,MAAM,kBAAkB,CAAC,cAAc,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;SAC3F;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,eAAiC;QAC9C,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,MAAM,cAAc,IAAI,IAAI,GAAG,CAAC,eAAe,CAAC,EAAE;YACrD,OAAO,CAAC,cAAc,CAAC,GAAG,MAAM,kBAAkB,CAAC,cAAc,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;SAC1F;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAaF,MAAM,UAAU,0BAA0B;IACxC,IAAI,OAAO,iBAAiB,KAAK,WAAW,IAAI,CAAC,CAAC,iBAAiB,EAAE,iBAAiB,EAAE;QACtF,OAAO,iBAAiB,CAAC,iBAAiB,CAAC;KAC5C;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,8CAA8C;AAC9C,SAAS,KAAK;IACZ,MAAM,OAAO,GAAG,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAChE,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACpC,OAAO,OAAO,IAAI,CAAC,MAAM,CAAC;AAC5B,CAAC", "sourcesContent": ["import {\n  PermissionMap,\n  PermissionType,\n  PermissionStatus,\n  PermissionInfo,\n} from './Permissions.types';\n\n/*\n * TODO: Bacon: https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Permissions\n * Add messages to manifest like we do with iOS info.plist\n */\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Using_the_new_API_in_older_browsers\n// Older browsers might not implement mediaDevices at all, so we set an empty object first\nfunction _getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n\n  // First get ahold of the legacy getUserMedia, if present\n  const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia ||\n    navigator.webkitGetUserMedia ||\n    navigator.mozGetUserMedia ||\n    function () {\n      const error: any = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n\n  return new Promise((resolve, reject) => {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\n\nasync function askForMediaPermissionAsync(\n  options: MediaStreamConstraints\n): Promise<PermissionInfo> {\n  try {\n    await _getUserMedia(options);\n    return {\n      status: PermissionStatus.GRANTED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: true,\n    };\n  } catch ({ message }) {\n    // name: NotAllowedError\n    // code: 0\n    if (message === 'Permission dismissed') {\n      // message: Permission dismissed\n      return {\n        status: PermissionStatus.UNDETERMINED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    } else {\n      // TODO: Bacon: [OSX] The system could deny access to chrome.\n      // TODO: Bacon: add: { status: 'unimplemented' }\n      // message: Permission denied\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    }\n  }\n}\n\nasync function askForMicrophonePermissionAsync(): Promise<PermissionInfo> {\n  return await askForMediaPermissionAsync({ audio: true });\n}\n\nasync function askForCameraPermissionAsync(): Promise<PermissionInfo> {\n  return await askForMediaPermissionAsync({ video: true });\n}\n\nasync function askForLocationPermissionAsync(): Promise<PermissionInfo> {\n  return new Promise((resolve) => {\n    navigator.geolocation.getCurrentPosition(\n      () =>\n        resolve({\n          status: PermissionStatus.GRANTED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: true,\n        }),\n      ({ code }: GeolocationPositionError) => {\n        // https://developer.mozilla.org/en-US/docs/Web/API/PositionError/code\n        if (code === 1) {\n          resolve({\n            status: PermissionStatus.DENIED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: false,\n          });\n        } else {\n          resolve({\n            status: PermissionStatus.UNDETERMINED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: false,\n          });\n        }\n      }\n    );\n  });\n}\n\nasync function getPermissionWithQueryAsync(\n  name: PermissionNameWithAdditionalValues\n): Promise<PermissionStatus | null> {\n  if (!navigator || !navigator.permissions || !navigator.permissions.query) return null;\n\n  const { state } = await navigator.permissions.query({ name });\n  if (state === 'prompt') {\n    return PermissionStatus.UNDETERMINED;\n  } else if (state === 'granted') {\n    return PermissionStatus.GRANTED;\n  } else if (state === 'denied') {\n    return PermissionStatus.DENIED;\n  }\n  return null;\n}\n\nasync function enumerateDevices(): Promise<MediaDeviceInfo[] | null> {\n  if (navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {\n    return await navigator.mediaDevices.enumerateDevices();\n  }\n\n  // @ts-ignore: This is deprecated but we should still attempt to use it.\n  if (window.MediaStreamTrack && typeof window.MediaStreamTrack.getSources === 'function') {\n    // @ts-ignore\n    return await MediaStreamTrack.getSources();\n  }\n  return null;\n}\n\nasync function askSensorPermissionAsync(): Promise<PermissionStatus> {\n  const requestPermission = getRequestMotionPermission();\n  // Technically this is incorrect because it doesn't account for iOS 12.2 Safari.\n  // But unfortunately we can only abstract so much.\n  if (!requestPermission) return PermissionStatus.GRANTED;\n\n  // If this isn't invoked in a touch-event then it never resolves.\n  // Safari probably should throw an error but because it doesn't we have no way of informing the developer.\n  const status = await requestPermission();\n  switch (status) {\n    case 'granted':\n      return PermissionStatus.GRANTED;\n    case 'denied':\n      return PermissionStatus.DENIED;\n    default:\n      return PermissionStatus.UNDETERMINED;\n  }\n}\n\nasync function getMediaMaybeGrantedAsync(targetKind: MediaDeviceKind): Promise<boolean> {\n  const devices = await enumerateDevices();\n  if (!devices) {\n    return false;\n  }\n  const result = await devices\n    .filter(({ kind }) => kind === targetKind)\n    .some(({ label }) => label !== '');\n  // Granted or denied or undetermined or no devices\n  return result;\n}\n\nasync function getPermissionAsync(\n  permission: PermissionType,\n  shouldAsk: boolean\n): Promise<PermissionInfo> {\n  switch (permission) {\n    case 'userFacingNotifications':\n    case 'notifications':\n      {\n        if (!shouldAsk) {\n          const status = await getPermissionWithQueryAsync('notifications');\n          if (status) {\n            return {\n              status,\n              expires: 'never',\n              granted: status === PermissionStatus.GRANTED,\n              canAskAgain: true,\n            };\n          }\n        }\n\n        const { Notification = {} } = window as any;\n        if (Notification.requestPermission) {\n          let status = Notification.permission;\n          if (shouldAsk) {\n            status = await Notification.requestPermission();\n          }\n          if (!status || status === 'default') {\n            return {\n              status: PermissionStatus.UNDETERMINED,\n              expires: 'never',\n              canAskAgain: true,\n              granted: false,\n            };\n          }\n          return {\n            status,\n            expires: 'never',\n            canAskAgain: true,\n            granted: status === PermissionStatus.GRANTED,\n          };\n        }\n      }\n      break;\n    case 'motion': {\n      if (shouldAsk) {\n        const status = await askSensorPermissionAsync();\n        return {\n          status,\n          expires: 'never',\n          granted: status === PermissionStatus.GRANTED,\n          canAskAgain: false,\n        };\n      }\n\n      // We can infer from the requestor if this is an older browser.\n      const status = getRequestMotionPermission()\n        ? PermissionStatus.UNDETERMINED\n        : isIOS()\n        ? PermissionStatus.DENIED\n        : PermissionStatus.GRANTED;\n      return {\n        status,\n        expires: 'never',\n        canAskAgain: true,\n        granted: status === PermissionStatus.GRANTED,\n      };\n    }\n    case 'location':\n    case 'locationForeground':\n    case 'locationBackground':\n      {\n        const maybeStatus = await getPermissionWithQueryAsync('geolocation');\n        if (maybeStatus) {\n          if (maybeStatus === PermissionStatus.UNDETERMINED && shouldAsk) {\n            return await askForLocationPermissionAsync();\n          }\n          return {\n            status: maybeStatus,\n            expires: 'never',\n            canAskAgain: true,\n            granted: maybeStatus === PermissionStatus.GRANTED,\n          };\n        } else if (shouldAsk) {\n          // TODO: Bacon: should this function as ask async when not in chrome?\n          return await askForLocationPermissionAsync();\n        }\n      }\n      break;\n    case 'audioRecording':\n      {\n        const maybeStatus = await getPermissionWithQueryAsync('microphone');\n        if (maybeStatus) {\n          if (maybeStatus === PermissionStatus.UNDETERMINED && shouldAsk) {\n            return await askForMicrophonePermissionAsync();\n          }\n          return {\n            status: maybeStatus,\n            expires: 'never',\n            canAskAgain: true,\n            granted: maybeStatus === PermissionStatus.GRANTED,\n          };\n        } else if (shouldAsk) {\n          return await askForMicrophonePermissionAsync();\n        } else {\n          const maybeGranted = await getMediaMaybeGrantedAsync('audioinput');\n          if (maybeGranted) {\n            return {\n              status: PermissionStatus.GRANTED,\n              expires: 'never',\n              canAskAgain: true,\n              granted: true,\n            };\n          }\n          // TODO: Bacon: Get denied or undetermined...\n        }\n      }\n      break;\n    case 'camera':\n      {\n        const maybeStatus = await getPermissionWithQueryAsync('camera');\n        if (maybeStatus) {\n          if (maybeStatus === PermissionStatus.UNDETERMINED && shouldAsk) {\n            return await askForCameraPermissionAsync();\n          }\n          return {\n            status: maybeStatus,\n            expires: 'never',\n            canAskAgain: true,\n            granted: maybeStatus === PermissionStatus.GRANTED,\n          };\n        } else if (shouldAsk) {\n          return await askForCameraPermissionAsync();\n        } else {\n          const maybeGranted = await getMediaMaybeGrantedAsync('videoinput');\n          if (maybeGranted) {\n            return {\n              status: PermissionStatus.GRANTED,\n              expires: 'never',\n              canAskAgain: true,\n              granted: true,\n            };\n          }\n          // TODO: Bacon: Get denied or undetermined...\n        }\n      }\n      break;\n    default:\n      break;\n  }\n  return {\n    status: PermissionStatus.UNDETERMINED,\n    expires: 'never',\n    canAskAgain: true,\n    granted: false,\n  };\n}\n\nexport default {\n  get name(): string {\n    return 'ExpoPermissions';\n  },\n\n  async getAsync(permissionTypes: PermissionType[]): Promise<PermissionMap> {\n    const results = {};\n    for (const permissionType of new Set(permissionTypes)) {\n      results[permissionType] = await getPermissionAsync(permissionType, /* shouldAsk */ false);\n    }\n    return results;\n  },\n\n  async askAsync(permissionTypes: PermissionType[]): Promise<PermissionMap> {\n    const results = {};\n    for (const permissionType of new Set(permissionTypes)) {\n      results[permissionType] = await getPermissionAsync(permissionType, /* shouldAsk */ true);\n    }\n    return results;\n  },\n};\n\n/**\n * Temporary solution until `tslib.d.ts` is updated to include the new DeviceMotion API.\n * `typescript@4.4.4` is missing `requestPermission` described in https://w3c.github.io/deviceorientation/#devicemotion\n * MDN docs do not describe this property as well https://developer.mozilla.org/en-US/docs/Web/API/DeviceMotionEvent\n */\ndeclare let DeviceMotionEvent: {\n  prototype: DeviceMotionEvent;\n  new (type: string, eventInitDict?: DeviceMotionEventInit): DeviceMotionEvent;\n  requestPermission?: () => Promise<PermissionState>;\n};\n\nexport function getRequestMotionPermission(): (() => Promise<PermissionState>) | null {\n  if (typeof DeviceMotionEvent !== 'undefined' && !!DeviceMotionEvent?.requestPermission) {\n    return DeviceMotionEvent.requestPermission;\n  }\n\n  return null;\n}\n\n// https://stackoverflow.com/a/9039885/4047926\nfunction isIOS(): boolean {\n  const isIOSUA = /(iPad|iPhone|iPod)/g.test(navigator.userAgent);\n  const isIE11 = !!window['MSStream'];\n  return isIOSUA && !isIE11;\n}\n"]}