/**
 * Tests for Gemini API Service
 */

import GeminiService from '../geminiService';

// Mock axios for testing
jest.mock('axios');

describe('GeminiService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('imageToBase64', () => {
    it('should convert image URI to base64', async () => {
      // Mock fetch and FileReader
      global.fetch = jest.fn().mockResolvedValue({
        blob: () => Promise.resolve(new Blob(['test'], { type: 'image/jpeg' })),
      });

      global.FileReader = jest.fn().mockImplementation(() => ({
        readAsDataURL: jest.fn(),
        onloadend: null,
        onerror: null,
        result: 'data:image/jpeg;base64,dGVzdA==',
      }));

      const mockFileReader = new FileReader();
      setTimeout(() => {
        mockFileReader.onloadend();
      }, 0);

      const result = await GeminiService.imageToBase64('file://test.jpg');
      expect(result).toBe('dGVzdA==');
    });

    it('should handle errors when converting image', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      await expect(GeminiService.imageToBase64('invalid-uri')).rejects.toThrow('Network error');
    });
  });

  describe('analyzeFoodImage', () => {
    it('should analyze food image successfully', async () => {
      const mockResponse = {
        data: {
          candidates: [
            {
              content: {
                parts: [
                  {
                    text: JSON.stringify({
                      foodItems: [
                        {
                          name: 'Apple',
                          portion: '1 medium',
                          calories: 95,
                          protein: 0.5,
                          carbs: 25,
                          fat: 0.3,
                        },
                      ],
                      totalNutrition: {
                        calories: 95,
                        protein: 0.5,
                        carbs: 25,
                        fat: 0.3,
                        fiber: 4,
                        sugar: 19,
                        sodium: 2,
                      },
                      confidence: 0.9,
                      notes: 'Clear image of a red apple',
                    }),
                  },
                ],
              },
            },
          ],
        },
      };

      // Mock the imageToBase64 method
      jest.spyOn(GeminiService, 'imageToBase64').mockResolvedValue('base64string');

      // Mock axios
      const axios = require('axios');
      axios.post.mockResolvedValue(mockResponse);

      const result = await GeminiService.analyzeFoodImage('file://test.jpg');

      expect(result.success).toBe(true);
      expect(result.data.foodItems).toHaveLength(1);
      expect(result.data.foodItems[0].name).toBe('Apple');
      expect(result.data.totalNutrition.calories).toBe(95);
    });

    it('should handle API errors gracefully', async () => {
      jest.spyOn(GeminiService, 'imageToBase64').mockResolvedValue('base64string');

      const axios = require('axios');
      axios.post.mockRejectedValue(new Error('API Error'));

      const result = await GeminiService.analyzeFoodImage('file://test.jpg');

      expect(result.success).toBe(false);
      expect(result.error).toBe('API Error');
      expect(result.data).toBe(null);
    });

    it('should parse text response when JSON parsing fails', async () => {
      const mockResponse = {
        data: {
          candidates: [
            {
              content: {
                parts: [
                  {
                    text: 'This appears to be an apple with approximately 95 calories.',
                  },
                ],
              },
            },
          ],
        },
      };

      jest.spyOn(GeminiService, 'imageToBase64').mockResolvedValue('base64string');

      const axios = require('axios');
      axios.post.mockResolvedValue(mockResponse);

      const result = await GeminiService.analyzeFoodImage('file://test.jpg');

      expect(result.success).toBe(true);
      expect(result.data.totalNutrition.calories).toBe(95);
      expect(result.data.confidence).toBe(0.1);
    });
  });

  describe('getFoodNutrition', () => {
    it('should get nutrition information for a specific food', async () => {
      const mockResponse = {
        data: {
          candidates: [
            {
              content: {
                parts: [
                  {
                    text: JSON.stringify({
                      name: 'Banana',
                      portion: '1 medium',
                      calories: 105,
                      protein: 1.3,
                      carbs: 27,
                      fat: 0.4,
                      fiber: 3.1,
                      sugar: 14,
                      sodium: 1,
                      vitamins: ['Vitamin C', 'Vitamin B6'],
                      minerals: ['Potassium', 'Manganese'],
                    }),
                  },
                ],
              },
            },
          ],
        },
      };

      const axios = require('axios');
      axios.post.mockResolvedValue(mockResponse);

      const result = await GeminiService.getFoodNutrition('Banana', '1 medium');

      expect(result.success).toBe(true);
      expect(result.data.name).toBe('Banana');
      expect(result.data.calories).toBe(105);
      expect(result.data.vitamins).toContain('Vitamin C');
    });

    it('should handle errors when getting food nutrition', async () => {
      const axios = require('axios');
      axios.post.mockRejectedValue(new Error('Network error'));

      const result = await GeminiService.getFoodNutrition('Unknown Food');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
      expect(result.data).toBe(null);
    });
  });

  describe('parseTextResponse', () => {
    it('should extract calories from text response', () => {
      const textResponse = 'This meal contains approximately 450 calories with good protein content.';
      
      const result = GeminiService.parseTextResponse(textResponse);

      expect(result.totalNutrition.calories).toBe(450);
      expect(result.foodItems[0].calories).toBe(450);
      expect(result.confidence).toBe(0.1);
    });

    it('should return default values when no calories found', () => {
      const textResponse = 'This is some food but no calorie information available.';
      
      const result = GeminiService.parseTextResponse(textResponse);

      expect(result.totalNutrition.calories).toBe(0);
      expect(result.confidence).toBe(0.1);
      expect(result.notes).toContain('Could not parse detailed nutritional information');
    });
  });
});
