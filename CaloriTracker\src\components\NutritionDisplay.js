import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { theme, commonStyles } from '../theme/theme';

/**
 * NutritionDisplay Component - Shows nutrition information with progress bars
 */
const NutritionDisplay = ({
  nutrition,
  goals,
  showPercentages = true,
  showBars = true,
  layout = 'vertical', // 'vertical' or 'horizontal'
  compact = false,
}) => {
  const calculatePercentage = (value, goal) => {
    if (!goal || goal === 0) return 0;
    return Math.min((value / goal) * 100, 100);
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 90) return theme.colors.success;
    if (percentage >= 70) return theme.colors.warning;
    return theme.colors.primary;
  };

  const renderNutritionItem = (label, value, unit, goal, color) => {
    const percentage = calculatePercentage(value, goal);
    const progressColor = getProgressColor(percentage);

    if (compact) {
      return (
        <View key={label} style={styles.compactItem}>
          <Text style={styles.compactValue}>{value}{unit}</Text>
          <Text style={styles.compactLabel}>{label}</Text>
        </View>
      );
    }

    return (
      <View key={label} style={[
        styles.nutritionItem,
        layout === 'horizontal' && styles.horizontalItem
      ]}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemLabel}>{label}</Text>
          <View style={styles.valueContainer}>
            <Text style={[styles.itemValue, { color }]}>
              {value}{unit}
            </Text>
            {goal && showPercentages && (
              <Text style={styles.goalText}>
                / {goal}{unit} ({Math.round(percentage)}%)
              </Text>
            )}
          </View>
        </View>
        
        {showBars && goal && (
          <View style={styles.progressContainer}>
            <View style={[commonStyles.nutritionBar, styles.progressBar]}>
              <View
                style={[
                  commonStyles.nutritionBarFill,
                  {
                    width: `${percentage}%`,
                    backgroundColor: progressColor,
                  },
                ]}
              />
            </View>
            <Text style={styles.remainingText}>
              {Math.max(0, goal - value)}{unit} remaining
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderMacronutrients = () => {
    const macros = [
      {
        label: 'Calories',
        value: nutrition.calories || 0,
        unit: '',
        goal: goals?.calories,
        color: theme.colors.calories,
      },
      {
        label: 'Protein',
        value: nutrition.protein || 0,
        unit: 'g',
        goal: goals?.protein,
        color: theme.colors.protein,
      },
      {
        label: 'Carbs',
        value: nutrition.carbs || 0,
        unit: 'g',
        goal: goals?.carbs,
        color: theme.colors.carbs,
      },
      {
        label: 'Fat',
        value: nutrition.fat || 0,
        unit: 'g',
        goal: goals?.fat,
        color: theme.colors.fat,
      },
    ];

    return macros.map(macro => 
      renderNutritionItem(
        macro.label,
        macro.value,
        macro.unit,
        macro.goal,
        macro.color
      )
    );
  };

  const renderMicronutrients = () => {
    const micros = [
      { label: 'Fiber', value: nutrition.fiber || 0, unit: 'g' },
      { label: 'Sugar', value: nutrition.sugar || 0, unit: 'g' },
      { label: 'Sodium', value: nutrition.sodium || 0, unit: 'mg' },
      { label: 'Cholesterol', value: nutrition.cholesterol || 0, unit: 'mg' },
    ];

    return (
      <View style={styles.micronutrientsContainer}>
        <Text style={styles.sectionTitle}>Other Nutrients</Text>
        <View style={styles.microGrid}>
          {micros.map(micro => (
            <View key={micro.label} style={styles.microItem}>
              <Text style={styles.microValue}>
                {micro.value}{micro.unit}
              </Text>
              <Text style={styles.microLabel}>{micro.label}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderCaloriesSummary = () => {
    if (!goals?.calories) return null;

    const consumed = nutrition.calories || 0;
    const remaining = Math.max(0, goals.calories - consumed);
    const percentage = calculatePercentage(consumed, goals.calories);

    return (
      <View style={styles.caloriesSummary}>
        <Text style={styles.caloriesTitle}>Daily Calories</Text>
        <View style={styles.caloriesRow}>
          <View style={styles.caloriesItem}>
            <Text style={styles.caloriesValue}>{consumed}</Text>
            <Text style={styles.caloriesLabel}>Consumed</Text>
          </View>
          <View style={styles.caloriesItem}>
            <Text style={styles.caloriesValue}>{remaining}</Text>
            <Text style={styles.caloriesLabel}>Remaining</Text>
          </View>
          <View style={styles.caloriesItem}>
            <Text style={styles.caloriesValue}>{goals.calories}</Text>
            <Text style={styles.caloriesLabel}>Goal</Text>
          </View>
        </View>
        <View style={styles.caloriesProgress}>
          <View style={[commonStyles.nutritionBar, { height: 12 }]}>
            <View
              style={[
                commonStyles.nutritionBarFill,
                {
                  width: `${percentage}%`,
                  backgroundColor: getProgressColor(percentage),
                },
              ]}
            />
          </View>
          <Text style={styles.caloriesPercentage}>
            {Math.round(percentage)}% of daily goal
          </Text>
        </View>
      </View>
    );
  };

  if (compact) {
    return (
      <View style={[styles.container, styles.compactContainer]}>
        {renderMacronutrients()}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderCaloriesSummary()}
      
      <View style={[
        styles.macroContainer,
        layout === 'horizontal' && styles.horizontalContainer
      ]}>
        {renderMacronutrients()}
      </View>
      
      {renderMicronutrients()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
  },
  
  compactContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: theme.spacing.sm,
  },
  
  compactItem: {
    alignItems: 'center',
  },
  
  compactValue: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: 2,
  },
  
  compactLabel: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
  },
  
  caloriesSummary: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  
  caloriesTitle: {
    ...theme.typography.h6,
    color: theme.colors.onPrimary,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  
  caloriesRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.sm,
  },
  
  caloriesItem: {
    alignItems: 'center',
  },
  
  caloriesValue: {
    ...theme.typography.h4,
    color: theme.colors.onPrimary,
    fontWeight: 'bold',
  },
  
  caloriesLabel: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    opacity: 0.8,
  },
  
  caloriesProgress: {
    marginTop: theme.spacing.sm,
  },
  
  caloriesPercentage: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  
  macroContainer: {
    marginBottom: theme.spacing.lg,
  },
  
  horizontalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  
  nutritionItem: {
    marginBottom: theme.spacing.md,
  },
  
  horizontalItem: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  
  itemLabel: {
    ...theme.typography.body2,
    color: theme.colors.onSurface,
    fontWeight: '600',
  },
  
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  itemValue: {
    ...theme.typography.h6,
    fontWeight: 'bold',
  },
  
  goalText: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
    marginLeft: theme.spacing.xs,
  },
  
  progressContainer: {
    marginTop: theme.spacing.xs,
  },
  
  progressBar: {
    marginBottom: theme.spacing.xs,
  },
  
  remainingText: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
  },
  
  micronutrientsContainer: {
    marginTop: theme.spacing.lg,
  },
  
  sectionTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.sm,
  },
  
  microGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  
  microItem: {
    width: '48%',
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    alignItems: 'center',
  },
  
  microValue: {
    ...theme.typography.body1,
    color: theme.colors.onSurface,
    fontWeight: '600',
    marginBottom: 2,
  },
  
  microLabel: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
  },
});

export default NutritionDisplay;
