import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'react-native-chart-kit';
import { theme } from '../theme/theme';
import { CHART_CONFIG } from '../config/config';

const screenWidth = Dimensions.get('window').width;

/**
 * NutritionChart Component - Various chart types for nutrition data visualization
 */
const NutritionChart = ({
  type = 'line', // 'line', 'bar', 'pie', 'progress'
  data,
  title,
  height = 220,
  showLegend = true,
  style,
}) => {
  const chartConfig = {
    ...CHART_CONFIG,
    color: (opacity = 1) => `rgba(98, 0, 238, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.7,
    useShadowColorFromDataset: false,
  };

  const renderLine<PERSON>hart = () => {
    if (!data || !data.datasets) return null;

    const lineData = {
      labels: data.labels || [],
      datasets: data.datasets.map((dataset, index) => ({
        data: dataset.data || [],
        color: (opacity = 1) => dataset.color || theme.colors.chartColors[index % theme.colors.chartColors.length],
        strokeWidth: 2,
      })),
      legend: showLegend ? data.legend : undefined,
    };

    return (
      <LineChart
        data={lineData}
        width={screenWidth - theme.spacing.md * 2}
        height={height}
        chartConfig={chartConfig}
        bezier
        style={styles.chart}
        withDots={true}
        withShadow={false}
        withScrollableDot={true}
        withInnerLines={false}
        withOuterLines={false}
      />
    );
  };

  const renderBarChart = () => {
    if (!data || !data.datasets) return null;

    const barData = {
      labels: data.labels || [],
      datasets: data.datasets,
    };

    return (
      <BarChart
        data={barData}
        width={screenWidth - theme.spacing.md * 2}
        height={height}
        chartConfig={chartConfig}
        style={styles.chart}
        showValuesOnTopOfBars={true}
        withInnerLines={false}
        fromZero={true}
      />
    );
  };

  const renderPieChart = () => {
    if (!data || !Array.isArray(data)) return null;

    const pieData = data.map((item, index) => ({
      name: item.name,
      population: item.value,
      color: item.color || theme.colors.chartColors[index % theme.colors.chartColors.length],
      legendFontColor: theme.colors.onBackground,
      legendFontSize: 12,
    }));

    return (
      <PieChart
        data={pieData}
        width={screenWidth - theme.spacing.md * 2}
        height={height}
        chartConfig={chartConfig}
        accessor="population"
        backgroundColor="transparent"
        paddingLeft="15"
        center={[10, 10]}
        absolute={false}
        hasLegend={showLegend}
        style={styles.chart}
      />
    );
  };

  const renderProgressChart = () => {
    if (!data || !data.data) return null;

    const progressData = {
      labels: data.labels || [],
      data: data.data,
      colors: data.colors || theme.colors.chartColors,
    };

    return (
      <ProgressChart
        data={progressData}
        width={screenWidth - theme.spacing.md * 2}
        height={height}
        strokeWidth={16}
        radius={32}
        chartConfig={chartConfig}
        hideLegend={!showLegend}
        style={styles.chart}
      />
    );
  };

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart();
      case 'bar':
        return renderBarChart();
      case 'pie':
        return renderPieChart();
      case 'progress':
        return renderProgressChart();
      default:
        return renderLineChart();
    }
  };

  const renderLegend = () => {
    if (!showLegend || !data.legend) return null;

    return (
      <View style={styles.legendContainer}>
        {data.legend.map((item, index) => (
          <View key={index} style={styles.legendItem}>
            <View
              style={[
                styles.legendColor,
                {
                  backgroundColor: item.color || theme.colors.chartColors[index % theme.colors.chartColors.length],
                },
              ]}
            />
            <Text style={styles.legendText}>{item.name}</Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {title && <Text style={styles.title}>{title}</Text>}
      {renderChart()}
      {renderLegend()}
    </View>
  );
};

/**
 * Predefined chart components for common use cases
 */

// Weekly Calories Chart
export const WeeklyCaloriesChart = ({ weeklyData, goal }) => {
  const data = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: weeklyData || [0, 0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => theme.colors.calories,
      },
      {
        data: Array(7).fill(goal || 2000),
        color: (opacity = 1) => theme.colors.gray400,
        strokeDashArray: [5, 5],
      },
    ],
    legend: [
      { name: 'Actual', color: theme.colors.calories },
      { name: 'Goal', color: theme.colors.gray400 },
    ],
  };

  return (
    <NutritionChart
      type="line"
      data={data}
      title="Weekly Calories"
      height={200}
    />
  );
};

// Macronutrient Distribution Pie Chart
export const MacroDistributionChart = ({ nutrition }) => {
  const totalCalories = nutrition.calories || 1;
  const proteinCal = (nutrition.protein || 0) * 4;
  const carbsCal = (nutrition.carbs || 0) * 4;
  const fatCal = (nutrition.fat || 0) * 9;

  const data = [
    {
      name: 'Protein',
      value: Math.round((proteinCal / totalCalories) * 100),
      color: theme.colors.protein,
    },
    {
      name: 'Carbs',
      value: Math.round((carbsCal / totalCalories) * 100),
      color: theme.colors.carbs,
    },
    {
      name: 'Fat',
      value: Math.round((fatCal / totalCalories) * 100),
      color: theme.colors.fat,
    },
  ];

  return (
    <NutritionChart
      type="pie"
      data={data}
      title="Macronutrient Distribution"
      height={200}
    />
  );
};

// Daily Progress Chart
export const DailyProgressChart = ({ nutrition, goals }) => {
  const data = {
    labels: ['Cal', 'Protein', 'Carbs', 'Fat'],
    data: [
      Math.min((nutrition.calories || 0) / (goals.calories || 1), 1),
      Math.min((nutrition.protein || 0) / (goals.protein || 1), 1),
      Math.min((nutrition.carbs || 0) / (goals.carbs || 1), 1),
      Math.min((nutrition.fat || 0) / (goals.fat || 1), 1),
    ],
    colors: [
      theme.colors.calories,
      theme.colors.protein,
      theme.colors.carbs,
      theme.colors.fat,
    ],
  };

  return (
    <NutritionChart
      type="progress"
      data={data}
      title="Daily Progress"
      height={180}
    />
  );
};

// Monthly Trend Chart
export const MonthlyTrendChart = ({ monthlyData }) => {
  const data = {
    labels: monthlyData?.labels || [],
    datasets: [
      {
        data: monthlyData?.calories || [],
        color: (opacity = 1) => theme.colors.calories,
      },
    ],
    legend: [
      { name: 'Average Daily Calories', color: theme.colors.calories },
    ],
  };

  return (
    <NutritionChart
      type="bar"
      data={data}
      title="Monthly Trend"
      height={220}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.sm,
    ...theme.shadows.md,
  },
  
  title: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  
  chart: {
    marginVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: theme.spacing.sm,
  },
  
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: theme.spacing.sm,
    marginVertical: theme.spacing.xs,
  },
  
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: theme.spacing.xs,
  },
  
  legendText: {
    ...theme.typography.caption,
    color: theme.colors.onSurface,
  },
});

export default NutritionChart;
