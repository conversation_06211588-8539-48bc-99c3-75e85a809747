int attr hideWheelUntilFocused 0x0
int attr internalLayout 0x0
int attr internalMaxHeight 0x0
int attr internalMaxWidth 0x0
int attr internalMinHeight 0x0
int attr internalMinWidth 0x0
int attr numberPickerStyle 0x0
int attr selectionDivider 0x0
int attr selectionDividerHeight 0x0
int attr selectionDividersDistance 0x0
int attr solidColor 0x0
int attr virtualButtonPressedDrawable 0x0
int drawable number_picker_divider_material 0x0
int drawable overlay 0x0
int id ampm 0x0
int id container 0x0
int id date 0x0
int id day 0x0
int id hour 0x0
int id minutes 0x0
int id month 0x0
int id numberpicker_input 0x0
int id overlay_image 0x0
int id pickerWrapper 0x0
int id year 0x0
int layout native_picker 0x0
int layout number_picker_material 0x0
int string ampm_description 0x0
int string date_description 0x0
int string day_description 0x0
int string hour_description 0x0
int string hour_tag 0x0
int string minutes_description 0x0
int string minutes_tag 0x0
int string month_description 0x0
int string overlay 0x0
int string time_tag 0x0
int string year_description 0x0
int style DatePickerBaseTheme 0x0
int style DatePickerTheme 0x0
int style android_native 0x0
int style android_native_small 0x0
int[] styleable NumberPicker { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable NumberPicker_hideWheelUntilFocused 0
int styleable NumberPicker_internalLayout 1
int styleable NumberPicker_internalMaxHeight 2
int styleable NumberPicker_internalMaxWidth 3
int styleable NumberPicker_internalMinHeight 4
int styleable NumberPicker_internalMinWidth 5
int styleable NumberPicker_selectionDivider 6
int styleable NumberPicker_selectionDividerHeight 7
int styleable NumberPicker_selectionDividersDistance 8
int styleable NumberPicker_solidColor 9
int styleable NumberPicker_virtualButtonPressedDrawable 10
int[] styleable Theme { 0x0 }
int styleable Theme_numberPickerStyle 0
