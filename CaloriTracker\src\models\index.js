/**
 * Data Models for CaloriTracker App
 */

import { generateMealId } from '../utils/apiUtils';

/**
 * Nutrition information model
 */
export class NutritionInfo {
  constructor({
    calories = 0,
    protein = 0,
    carbs = 0,
    fat = 0,
    fiber = 0,
    sugar = 0,
    sodium = 0,
    cholesterol = 0,
    saturatedFat = 0,
    transFat = 0,
    potassium = 0,
    calcium = 0,
    iron = 0,
    vitaminC = 0,
    vitaminA = 0,
  } = {}) {
    this.calories = Math.round(calories);
    this.protein = Math.round(protein * 10) / 10;
    this.carbs = Math.round(carbs * 10) / 10;
    this.fat = Math.round(fat * 10) / 10;
    this.fiber = Math.round(fiber * 10) / 10;
    this.sugar = Math.round(sugar * 10) / 10;
    this.sodium = Math.round(sodium * 10) / 10;
    this.cholesterol = Math.round(cholesterol * 10) / 10;
    this.saturatedFat = Math.round(saturatedFat * 10) / 10;
    this.transFat = Math.round(transFat * 10) / 10;
    this.potassium = Math.round(potassium * 10) / 10;
    this.calcium = Math.round(calcium * 10) / 10;
    this.iron = Math.round(iron * 10) / 10;
    this.vitaminC = Math.round(vitaminC * 10) / 10;
    this.vitaminA = Math.round(vitaminA * 10) / 10;
  }

  /**
   * Add nutrition values from another NutritionInfo instance
   * @param {NutritionInfo} other - Other nutrition info to add
   * @returns {NutritionInfo} New NutritionInfo instance with combined values
   */
  add(other) {
    return new NutritionInfo({
      calories: this.calories + other.calories,
      protein: this.protein + other.protein,
      carbs: this.carbs + other.carbs,
      fat: this.fat + other.fat,
      fiber: this.fiber + other.fiber,
      sugar: this.sugar + other.sugar,
      sodium: this.sodium + other.sodium,
      cholesterol: this.cholesterol + other.cholesterol,
      saturatedFat: this.saturatedFat + other.saturatedFat,
      transFat: this.transFat + other.transFat,
      potassium: this.potassium + other.potassium,
      calcium: this.calcium + other.calcium,
      iron: this.iron + other.iron,
      vitaminC: this.vitaminC + other.vitaminC,
      vitaminA: this.vitaminA + other.vitaminA,
    });
  }

  /**
   * Scale nutrition values by a factor
   * @param {number} factor - Scaling factor
   * @returns {NutritionInfo} New NutritionInfo instance with scaled values
   */
  scale(factor) {
    return new NutritionInfo({
      calories: this.calories * factor,
      protein: this.protein * factor,
      carbs: this.carbs * factor,
      fat: this.fat * factor,
      fiber: this.fiber * factor,
      sugar: this.sugar * factor,
      sodium: this.sodium * factor,
      cholesterol: this.cholesterol * factor,
      saturatedFat: this.saturatedFat * factor,
      transFat: this.transFat * factor,
      potassium: this.potassium * factor,
      calcium: this.calcium * factor,
      iron: this.iron * factor,
      vitaminC: this.vitaminC * factor,
      vitaminA: this.vitaminA * factor,
    });
  }

  /**
   * Convert to plain object
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      calories: this.calories,
      protein: this.protein,
      carbs: this.carbs,
      fat: this.fat,
      fiber: this.fiber,
      sugar: this.sugar,
      sodium: this.sodium,
      cholesterol: this.cholesterol,
      saturatedFat: this.saturatedFat,
      transFat: this.transFat,
      potassium: this.potassium,
      calcium: this.calcium,
      iron: this.iron,
      vitaminC: this.vitaminC,
      vitaminA: this.vitaminA,
    };
  }

  /**
   * Create from plain object
   * @param {Object} obj - Plain object
   * @returns {NutritionInfo} NutritionInfo instance
   */
  static fromObject(obj) {
    return new NutritionInfo(obj);
  }
}

/**
 * Food item model
 */
export class FoodItem {
  constructor({
    id = null,
    name = '',
    portion = '1 serving',
    nutrition = new NutritionInfo(),
    confidence = 1.0,
    isManuallyEdited = false,
    notes = '',
  } = {}) {
    this.id = id || `food_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.name = name;
    this.portion = portion;
    this.nutrition = nutrition instanceof NutritionInfo ? nutrition : new NutritionInfo(nutrition);
    this.confidence = Math.max(0, Math.min(1, confidence));
    this.isManuallyEdited = isManuallyEdited;
    this.notes = notes;
  }

  /**
   * Update nutrition information
   * @param {Object} nutritionData - New nutrition data
   */
  updateNutrition(nutritionData) {
    this.nutrition = new NutritionInfo(nutritionData);
    this.isManuallyEdited = true;
  }

  /**
   * Convert to plain object
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      name: this.name,
      portion: this.portion,
      nutrition: this.nutrition.toObject(),
      confidence: this.confidence,
      isManuallyEdited: this.isManuallyEdited,
      notes: this.notes,
    };
  }

  /**
   * Create from plain object
   * @param {Object} obj - Plain object
   * @returns {FoodItem} FoodItem instance
   */
  static fromObject(obj) {
    return new FoodItem({
      ...obj,
      nutrition: NutritionInfo.fromObject(obj.nutrition || {}),
    });
  }
}

/**
 * Meal model
 */
export class Meal {
  constructor({
    id = null,
    name = '',
    type = 'other', // breakfast, lunch, dinner, snack, other
    timestamp = new Date(),
    imageUri = null,
    foodItems = [],
    totalNutrition = new NutritionInfo(),
    analysisConfidence = 1.0,
    isAnalyzed = false,
    isManuallyEdited = false,
    notes = '',
    tags = [],
  } = {}) {
    this.id = id || generateMealId();
    this.name = name;
    this.type = type;
    this.timestamp = timestamp instanceof Date ? timestamp : new Date(timestamp);
    this.imageUri = imageUri;
    this.foodItems = foodItems.map(item => 
      item instanceof FoodItem ? item : FoodItem.fromObject(item)
    );
    this.totalNutrition = totalNutrition instanceof NutritionInfo ? 
      totalNutrition : new NutritionInfo(totalNutrition);
    this.analysisConfidence = Math.max(0, Math.min(1, analysisConfidence));
    this.isAnalyzed = isAnalyzed;
    this.isManuallyEdited = isManuallyEdited;
    this.notes = notes;
    this.tags = Array.isArray(tags) ? tags : [];
  }

  /**
   * Add food item to meal
   * @param {FoodItem} foodItem - Food item to add
   */
  addFoodItem(foodItem) {
    const item = foodItem instanceof FoodItem ? foodItem : FoodItem.fromObject(foodItem);
    this.foodItems.push(item);
    this.calculateTotalNutrition();
  }

  /**
   * Remove food item from meal
   * @param {string} foodItemId - ID of food item to remove
   */
  removeFoodItem(foodItemId) {
    this.foodItems = this.foodItems.filter(item => item.id !== foodItemId);
    this.calculateTotalNutrition();
  }

  /**
   * Update food item in meal
   * @param {string} foodItemId - ID of food item to update
   * @param {Object} updates - Updates to apply
   */
  updateFoodItem(foodItemId, updates) {
    const itemIndex = this.foodItems.findIndex(item => item.id === foodItemId);
    if (itemIndex !== -1) {
      this.foodItems[itemIndex] = { ...this.foodItems[itemIndex], ...updates };
      this.calculateTotalNutrition();
      this.isManuallyEdited = true;
    }
  }

  /**
   * Calculate total nutrition from food items
   */
  calculateTotalNutrition() {
    this.totalNutrition = this.foodItems.reduce(
      (total, item) => total.add(item.nutrition),
      new NutritionInfo()
    );
  }

  /**
   * Get meal date (without time)
   * @returns {string} Date string in YYYY-MM-DD format
   */
  getDateString() {
    return this.timestamp.toISOString().split('T')[0];
  }

  /**
   * Check if meal is from today
   * @returns {boolean} Whether meal is from today
   */
  isToday() {
    const today = new Date();
    return this.getDateString() === today.toISOString().split('T')[0];
  }

  /**
   * Convert to plain object
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      timestamp: this.timestamp.toISOString(),
      imageUri: this.imageUri,
      foodItems: this.foodItems.map(item => item.toObject()),
      totalNutrition: this.totalNutrition.toObject(),
      analysisConfidence: this.analysisConfidence,
      isAnalyzed: this.isAnalyzed,
      isManuallyEdited: this.isManuallyEdited,
      notes: this.notes,
      tags: this.tags,
    };
  }

  /**
   * Create from plain object
   * @param {Object} obj - Plain object
   * @returns {Meal} Meal instance
   */
  static fromObject(obj) {
    return new Meal({
      ...obj,
      timestamp: new Date(obj.timestamp),
      foodItems: (obj.foodItems || []).map(item => FoodItem.fromObject(item)),
      totalNutrition: NutritionInfo.fromObject(obj.totalNutrition || {}),
    });
  }
}

/**
 * User profile model
 */
export class UserProfile {
  constructor({
    id = 'default',
    name = '',
    age = null,
    gender = '', // male, female, other
    height = null, // in cm
    weight = null, // in kg
    activityLevel = 'moderate', // sedentary, light, moderate, active, very_active
    goal = 'maintain', // lose, maintain, gain
    dailyCalorieGoal = 2000,
    dailyProteinGoal = 150,
    dailyCarbGoal = 250,
    dailyFatGoal = 65,
    createdAt = new Date(),
    updatedAt = new Date(),
  } = {}) {
    this.id = id;
    this.name = name;
    this.age = age;
    this.gender = gender;
    this.height = height;
    this.weight = weight;
    this.activityLevel = activityLevel;
    this.goal = goal;
    this.dailyCalorieGoal = dailyCalorieGoal;
    this.dailyProteinGoal = dailyProteinGoal;
    this.dailyCarbGoal = dailyCarbGoal;
    this.dailyFatGoal = dailyFatGoal;
    this.createdAt = createdAt instanceof Date ? createdAt : new Date(createdAt);
    this.updatedAt = updatedAt instanceof Date ? updatedAt : new Date(updatedAt);
  }

  /**
   * Calculate BMI if height and weight are available
   * @returns {number|null} BMI value or null
   */
  calculateBMI() {
    if (!this.height || !this.weight) return null;
    const heightInMeters = this.height / 100;
    return Math.round((this.weight / (heightInMeters * heightInMeters)) * 10) / 10;
  }

  /**
   * Get BMI category
   * @returns {string|null} BMI category or null
   */
  getBMICategory() {
    const bmi = this.calculateBMI();
    if (!bmi) return null;

    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal weight';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }

  /**
   * Update profile
   * @param {Object} updates - Updates to apply
   */
  update(updates) {
    Object.assign(this, updates);
    this.updatedAt = new Date();
  }

  /**
   * Convert to plain object
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      name: this.name,
      age: this.age,
      gender: this.gender,
      height: this.height,
      weight: this.weight,
      activityLevel: this.activityLevel,
      goal: this.goal,
      dailyCalorieGoal: this.dailyCalorieGoal,
      dailyProteinGoal: this.dailyProteinGoal,
      dailyCarbGoal: this.dailyCarbGoal,
      dailyFatGoal: this.dailyFatGoal,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
    };
  }

  /**
   * Create from plain object
   * @param {Object} obj - Plain object
   * @returns {UserProfile} UserProfile instance
   */
  static fromObject(obj) {
    return new UserProfile({
      ...obj,
      createdAt: new Date(obj.createdAt),
      updatedAt: new Date(obj.updatedAt),
    });
  }
}

export default {
  NutritionInfo,
  FoodItem,
  Meal,
  UserProfile,
};
