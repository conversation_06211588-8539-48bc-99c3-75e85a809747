import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../config/config';
import { Meal, UserProfile } from '../models';

/**
 * Storage Service for managing app data persistence
 */
class StorageService {
  constructor() {
    this.initialized = false;
  }

  /**
   * Initialize storage service
   */
  async initialize() {
    try {
      // Perform any initialization tasks
      this.initialized = true;
      console.log('Storage service initialized');
    } catch (error) {
      console.error('Error initializing storage service:', error);
      throw error;
    }
  }

  /**
   * Generic method to store data
   * @param {string} key - Storage key
   * @param {any} data - Data to store
   * @returns {Promise<boolean>} Success status
   */
  async setItem(key, data) {
    try {
      const jsonData = JSON.stringify(data);
      await AsyncStorage.setItem(key, jsonData);
      return true;
    } catch (error) {
      console.error(`Error storing data for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Generic method to retrieve data
   * @param {string} key - Storage key
   * @param {any} defaultValue - Default value if key doesn't exist
   * @returns {Promise<any>} Retrieved data or default value
   */
  async getItem(key, defaultValue = null) {
    try {
      const jsonData = await AsyncStorage.getItem(key);
      if (jsonData === null) {
        return defaultValue;
      }
      return JSON.parse(jsonData);
    } catch (error) {
      console.error(`Error retrieving data for key ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Generic method to remove data
   * @param {string} key - Storage key
   * @returns {Promise<boolean>} Success status
   */
  async removeItem(key) {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Error removing data for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Clear all app data
   * @returns {Promise<boolean>} Success status
   */
  async clearAll() {
    try {
      const keys = Object.values(STORAGE_KEYS);
      await AsyncStorage.multiRemove(keys);
      return true;
    } catch (error) {
      console.error('Error clearing all data:', error);
      return false;
    }
  }

  // MEAL OPERATIONS

  /**
   * Save a meal
   * @param {Meal} meal - Meal to save
   * @returns {Promise<boolean>} Success status
   */
  async saveMeal(meal) {
    try {
      const meals = await this.getAllMeals();
      const existingIndex = meals.findIndex(m => m.id === meal.id);
      
      if (existingIndex !== -1) {
        meals[existingIndex] = meal.toObject();
      } else {
        meals.push(meal.toObject());
      }

      return await this.setItem(STORAGE_KEYS.MEALS, meals);
    } catch (error) {
      console.error('Error saving meal:', error);
      return false;
    }
  }

  /**
   * Get all meals
   * @returns {Promise<Meal[]>} Array of meals
   */
  async getAllMeals() {
    try {
      const mealsData = await this.getItem(STORAGE_KEYS.MEALS, []);
      return mealsData.map(mealData => Meal.fromObject(mealData));
    } catch (error) {
      console.error('Error getting all meals:', error);
      return [];
    }
  }

  /**
   * Get meal by ID
   * @param {string} mealId - Meal ID
   * @returns {Promise<Meal|null>} Meal or null if not found
   */
  async getMealById(mealId) {
    try {
      const meals = await this.getAllMeals();
      const meal = meals.find(m => m.id === mealId);
      return meal || null;
    } catch (error) {
      console.error('Error getting meal by ID:', error);
      return null;
    }
  }

  /**
   * Get meals for a specific date
   * @param {Date} date - Date to filter by
   * @returns {Promise<Meal[]>} Array of meals for the date
   */
  async getMealsByDate(date) {
    try {
      const meals = await this.getAllMeals();
      const dateString = date.toISOString().split('T')[0];
      return meals.filter(meal => meal.getDateString() === dateString);
    } catch (error) {
      console.error('Error getting meals by date:', error);
      return [];
    }
  }

  /**
   * Get meals within a date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Meal[]>} Array of meals within the range
   */
  async getMealsByDateRange(startDate, endDate) {
    try {
      const meals = await this.getAllMeals();
      return meals.filter(meal => {
        const mealDate = meal.timestamp;
        return mealDate >= startDate && mealDate <= endDate;
      });
    } catch (error) {
      console.error('Error getting meals by date range:', error);
      return [];
    }
  }

  /**
   * Delete a meal
   * @param {string} mealId - Meal ID to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteMeal(mealId) {
    try {
      const meals = await this.getAllMeals();
      const filteredMeals = meals.filter(m => m.id !== mealId);
      const mealsData = filteredMeals.map(meal => meal.toObject());
      return await this.setItem(STORAGE_KEYS.MEALS, mealsData);
    } catch (error) {
      console.error('Error deleting meal:', error);
      return false;
    }
  }

  /**
   * Get today's meals
   * @returns {Promise<Meal[]>} Array of today's meals
   */
  async getTodaysMeals() {
    return await this.getMealsByDate(new Date());
  }

  // USER PROFILE OPERATIONS

  /**
   * Save user profile
   * @param {UserProfile} profile - User profile to save
   * @returns {Promise<boolean>} Success status
   */
  async saveUserProfile(profile) {
    try {
      return await this.setItem(STORAGE_KEYS.USER_PROFILE, profile.toObject());
    } catch (error) {
      console.error('Error saving user profile:', error);
      return false;
    }
  }

  /**
   * Get user profile
   * @returns {Promise<UserProfile>} User profile
   */
  async getUserProfile() {
    try {
      const profileData = await this.getItem(STORAGE_KEYS.USER_PROFILE);
      if (profileData) {
        return UserProfile.fromObject(profileData);
      } else {
        // Return default profile if none exists
        return new UserProfile();
      }
    } catch (error) {
      console.error('Error getting user profile:', error);
      return new UserProfile();
    }
  }

  // DAILY GOALS OPERATIONS

  /**
   * Save daily nutrition goals
   * @param {Object} goals - Daily nutrition goals
   * @returns {Promise<boolean>} Success status
   */
  async saveDailyGoals(goals) {
    try {
      return await this.setItem(STORAGE_KEYS.DAILY_GOALS, goals);
    } catch (error) {
      console.error('Error saving daily goals:', error);
      return false;
    }
  }

  /**
   * Get daily nutrition goals
   * @returns {Promise<Object>} Daily nutrition goals
   */
  async getDailyGoals() {
    try {
      return await this.getItem(STORAGE_KEYS.DAILY_GOALS, {
        calories: 2000,
        protein: 150,
        carbs: 250,
        fat: 65,
      });
    } catch (error) {
      console.error('Error getting daily goals:', error);
      return {
        calories: 2000,
        protein: 150,
        carbs: 250,
        fat: 65,
      };
    }
  }

  // APP SETTINGS OPERATIONS

  /**
   * Save app settings
   * @param {Object} settings - App settings
   * @returns {Promise<boolean>} Success status
   */
  async saveAppSettings(settings) {
    try {
      return await this.setItem(STORAGE_KEYS.APP_SETTINGS, settings);
    } catch (error) {
      console.error('Error saving app settings:', error);
      return false;
    }
  }

  /**
   * Get app settings
   * @returns {Promise<Object>} App settings
   */
  async getAppSettings() {
    try {
      return await this.getItem(STORAGE_KEYS.APP_SETTINGS, {
        theme: 'light',
        notifications: true,
        units: 'metric', // metric or imperial
        language: 'en',
      });
    } catch (error) {
      console.error('Error getting app settings:', error);
      return {
        theme: 'light',
        notifications: true,
        units: 'metric',
        language: 'en',
      };
    }
  }

  // ANALYTICS AND STATISTICS

  /**
   * Get nutrition statistics for a date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Object>} Nutrition statistics
   */
  async getNutritionStats(startDate, endDate) {
    try {
      const meals = await this.getMealsByDateRange(startDate, endDate);
      
      if (meals.length === 0) {
        return {
          totalMeals: 0,
          avgCalories: 0,
          avgProtein: 0,
          avgCarbs: 0,
          avgFat: 0,
          totalCalories: 0,
          totalProtein: 0,
          totalCarbs: 0,
          totalFat: 0,
        };
      }

      const totals = meals.reduce(
        (acc, meal) => ({
          calories: acc.calories + meal.totalNutrition.calories,
          protein: acc.protein + meal.totalNutrition.protein,
          carbs: acc.carbs + meal.totalNutrition.carbs,
          fat: acc.fat + meal.totalNutrition.fat,
        }),
        { calories: 0, protein: 0, carbs: 0, fat: 0 }
      );

      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;

      return {
        totalMeals: meals.length,
        avgCalories: Math.round(totals.calories / days),
        avgProtein: Math.round((totals.protein / days) * 10) / 10,
        avgCarbs: Math.round((totals.carbs / days) * 10) / 10,
        avgFat: Math.round((totals.fat / days) * 10) / 10,
        totalCalories: totals.calories,
        totalProtein: totals.protein,
        totalCarbs: totals.carbs,
        totalFat: totals.fat,
      };
    } catch (error) {
      console.error('Error getting nutrition stats:', error);
      return null;
    }
  }

  /**
   * Export all data
   * @returns {Promise<Object>} All app data
   */
  async exportData() {
    try {
      const [meals, profile, goals, settings] = await Promise.all([
        this.getItem(STORAGE_KEYS.MEALS, []),
        this.getItem(STORAGE_KEYS.USER_PROFILE),
        this.getItem(STORAGE_KEYS.DAILY_GOALS),
        this.getItem(STORAGE_KEYS.APP_SETTINGS),
      ]);

      return {
        meals,
        profile,
        goals,
        settings,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
    } catch (error) {
      console.error('Error exporting data:', error);
      return null;
    }
  }

  /**
   * Import data
   * @param {Object} data - Data to import
   * @returns {Promise<boolean>} Success status
   */
  async importData(data) {
    try {
      if (data.meals) {
        await this.setItem(STORAGE_KEYS.MEALS, data.meals);
      }
      if (data.profile) {
        await this.setItem(STORAGE_KEYS.USER_PROFILE, data.profile);
      }
      if (data.goals) {
        await this.setItem(STORAGE_KEYS.DAILY_GOALS, data.goals);
      }
      if (data.settings) {
        await this.setItem(STORAGE_KEYS.APP_SETTINGS, data.settings);
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }
}

export default new StorageService();
