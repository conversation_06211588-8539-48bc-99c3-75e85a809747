/**
 * API Utility functions for handling requests, responses, and errors
 */

/**
 * Handle API response and extract data
 * @param {Object} response - Axios response object
 * @returns {Object} Processed response
 */
export const handleApiResponse = (response) => {
  if (response.status >= 200 && response.status < 300) {
    return {
      success: true,
      data: response.data,
      status: response.status,
    };
  } else {
    return {
      success: false,
      error: `HTTP ${response.status}: ${response.statusText}`,
      status: response.status,
    };
  }
};

/**
 * Handle API errors
 * @param {Error} error - Error object
 * @returns {Object} Formatted error response
 */
export const handleApiError = (error) => {
  console.error('API Error:', error);

  if (error.response) {
    // Server responded with error status
    return {
      success: false,
      error: error.response.data?.message || error.response.statusText || 'Server error',
      status: error.response.status,
      details: error.response.data,
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      success: false,
      error: 'Network error - please check your internet connection',
      status: null,
      details: 'No response from server',
    };
  } else {
    // Something else happened
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
      status: null,
      details: error.toString(),
    };
  }
};

/**
 * Validate image file
 * @param {string} imageUri - Image URI to validate
 * @returns {Object} Validation result
 */
export const validateImage = (imageUri) => {
  if (!imageUri) {
    return {
      isValid: false,
      error: 'No image provided',
    };
  }

  // Check if it's a valid URI format
  const uriPattern = /^(file:\/\/|content:\/\/|data:image\/)/;
  if (!uriPattern.test(imageUri)) {
    return {
      isValid: false,
      error: 'Invalid image URI format',
    };
  }

  return {
    isValid: true,
    error: null,
  };
};

/**
 * Retry API call with exponential backoff
 * @param {Function} apiCall - Function that returns a Promise
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Result of the API call
 */
export const retryApiCall = async (apiCall, maxRetries = 3, baseDelay = 1000) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await apiCall();
      return result;
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`API call failed, retrying in ${delay}ms... (attempt ${attempt + 1}/${maxRetries + 1})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

/**
 * Format nutrition data for consistency
 * @param {Object} nutritionData - Raw nutrition data
 * @returns {Object} Formatted nutrition data
 */
export const formatNutritionData = (nutritionData) => {
  const defaultNutrition = {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  };

  if (!nutritionData) {
    return defaultNutrition;
  }

  return {
    calories: Math.round(nutritionData.calories || 0),
    protein: Math.round((nutritionData.protein || 0) * 10) / 10,
    carbs: Math.round((nutritionData.carbs || nutritionData.carbohydrates || 0) * 10) / 10,
    fat: Math.round((nutritionData.fat || nutritionData.fats || 0) * 10) / 10,
    fiber: Math.round((nutritionData.fiber || 0) * 10) / 10,
    sugar: Math.round((nutritionData.sugar || nutritionData.sugars || 0) * 10) / 10,
    sodium: Math.round((nutritionData.sodium || 0) * 10) / 10,
  };
};

/**
 * Calculate nutrition percentages based on daily goals
 * @param {Object} nutrition - Current nutrition values
 * @param {Object} goals - Daily nutrition goals
 * @returns {Object} Nutrition percentages
 */
export const calculateNutritionPercentages = (nutrition, goals) => {
  return {
    calories: goals.calories ? Math.round((nutrition.calories / goals.calories) * 100) : 0,
    protein: goals.protein ? Math.round((nutrition.protein / goals.protein) * 100) : 0,
    carbs: goals.carbs ? Math.round((nutrition.carbs / goals.carbs) * 100) : 0,
    fat: goals.fat ? Math.round((nutrition.fat / goals.fat) * 100) : 0,
  };
};

/**
 * Validate nutrition goals
 * @param {Object} goals - Nutrition goals to validate
 * @returns {Object} Validation result
 */
export const validateNutritionGoals = (goals) => {
  const errors = [];

  if (!goals.calories || goals.calories < 1200 || goals.calories > 4000) {
    errors.push('Calories should be between 1200 and 4000');
  }

  if (!goals.protein || goals.protein < 50 || goals.protein > 300) {
    errors.push('Protein should be between 50g and 300g');
  }

  if (!goals.carbs || goals.carbs < 100 || goals.carbs > 500) {
    errors.push('Carbohydrates should be between 100g and 500g');
  }

  if (!goals.fat || goals.fat < 30 || goals.fat > 200) {
    errors.push('Fat should be between 30g and 200g');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Generate unique ID for meals
 * @returns {string} Unique ID
 */
export const generateMealId = () => {
  return `meal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Format date for display
 * @param {Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDate = (date) => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Format time for display
 * @param {Date} date - Date to format
 * @returns {string} Formatted time string
 */
export const formatTime = (date) => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Get date range for filtering
 * @param {string} period - Period type ('today', 'week', 'month')
 * @returns {Object} Start and end dates
 */
export const getDateRange = (period) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (period) {
    case 'today':
      return {
        start: today,
        end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1),
      };
    case 'week':
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);
      return { start: weekStart, end: weekEnd };
    case 'month':
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      monthEnd.setHours(23, 59, 59, 999);
      return { start: monthStart, end: monthEnd };
    default:
      return { start: today, end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1) };
  }
};
