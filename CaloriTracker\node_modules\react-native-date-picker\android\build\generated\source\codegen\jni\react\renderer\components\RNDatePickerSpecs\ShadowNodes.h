
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/RNDatePickerSpecs/EventEmitters.h>
#include <react/renderer/components/RNDatePickerSpecs/Props.h>
#include <react/renderer/components/RNDatePickerSpecs/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNDatePickerComponentName[];

/*
 * `ShadowNode` for <RNDatePicker> component.
 */
using RNDatePickerShadowNode = ConcreteViewShadowNode<
    RNDatePickerComponentName,
    RNDatePickerProps,
    RNDatePickerEventEmitter,
    RNDatePickerState>;

} // namespace facebook::react
