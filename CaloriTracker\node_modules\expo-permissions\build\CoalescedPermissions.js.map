{"version": 3, "file": "CoalescedPermissions.js", "sourceRoot": "", "sources": ["../src/CoalescedPermissions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAuC,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAE5F,MAAM,UAAU,gBAAgB,CAAC,WAA0B;IACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;IAClF,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3F,6DAA6D;IAC7D,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAwB;IAChD,+FAA+F;IAC/F,+FAA+F;IAC/F,iCAAiC;IACjC,QAAQ,MAAM,EAAE;QACd,KAAK,gBAAgB,CAAC,OAAO;YAC3B,OAAO,CAAC,CAAC;QACX,KAAK,gBAAgB,CAAC,MAAM;YAC1B,OAAO,CAAC,CAAC;QACX,KAAK,gBAAgB,CAAC,YAAY;YAChC,OAAO,CAAC,CAAC;QACX;YACE,OAAO,GAAG,CAAC;KACd;AACH,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,WAA0B;IAC5D,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,0BAA0B;IAClE,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;IACtF,WAAW,CAAC,IAAI,CACd,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CACT,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;QACnD,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CACtD,CAAC;IACF,oCAAoC;IACpC,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,WAA0B;IAC5D,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CACpC,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EACnE,IAAI,CACL,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,WAA0B;IACxD,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CACpC,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,EACvD,IAAI,CACL,CAAC;AACJ,CAAC", "sourcesContent": ["import { PermissionExpiration, PermissionMap, PermissionStatus } from './Permissions.types';\n\nexport function coalesceStatuses(permissions: PermissionMap): PermissionStatus {\n  const statuses = Object.keys(permissions).map((type) => permissions[type].status);\n  statuses.sort((status1, status2) => _getStatusWeight(status1) - _getStatusWeight(status2));\n  // We choose the \"heaviest\" status with the most implications\n  return statuses[statuses.length - 1];\n}\n\nfunction _getStatusWeight(status: PermissionStatus): number {\n  // In terms of weight, we treat UNDETERMINED > DENIED > GRANTED since UNDETERMINED requires the\n  // most amount of further handling (prompting for permission and then checking that permission)\n  // and GRANTED requires the least\n  switch (status) {\n    case PermissionStatus.GRANTED:\n      return 0;\n    case PermissionStatus.DENIED:\n      return 1;\n    case PermissionStatus.UNDETERMINED:\n      return 2;\n    default:\n      return 100;\n  }\n}\n\nexport function coalesceExpirations(permissions: PermissionMap): PermissionExpiration {\n  const maxExpiration = 9007199254740991; // Number.MAX_SAFE_INTEGER\n  const expirations = Object.keys(permissions).map((type) => permissions[type].expires);\n  expirations.sort(\n    (e1, e2) =>\n      (e1 == null || e1 === 'never' ? maxExpiration : e1) -\n      (e2 == null || e2 === 'never' ? maxExpiration : e2)\n  );\n  // We choose the earliest expiration\n  return expirations[0];\n}\n\nexport function coalesceCanAskAgain(permissions: PermissionMap): boolean {\n  return Object.keys(permissions).reduce<boolean>(\n    (canAskAgain, type) => canAskAgain && permissions[type].canAskAgain,\n    true\n  );\n}\n\nexport function coalesceGranted(permissions: PermissionMap): boolean {\n  return Object.keys(permissions).reduce<boolean>(\n    (granted, type) => granted && permissions[type].granted,\n    true\n  );\n}\n"]}