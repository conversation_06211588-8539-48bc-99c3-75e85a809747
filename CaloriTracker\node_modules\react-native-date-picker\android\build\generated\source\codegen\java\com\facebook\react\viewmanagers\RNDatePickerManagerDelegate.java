/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.BaseViewManagerInterface;

public class RNDatePickerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNDatePickerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNDatePickerManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "locale":
        mViewManager.setLocale(view, value == null ? null : (String) value);
        break;
      case "date":
        mViewManager.setDate(view, value == null ? null : (String) value);
        break;
      case "maximumDate":
        mViewManager.setMaximumDate(view, value == null ? null : (String) value);
        break;
      case "minimumDate":
        mViewManager.setMinimumDate(view, value == null ? null : (String) value);
        break;
      case "minuteInterval":
        mViewManager.setMinuteInterval(view, value == null ? 0 : ((Double) value).intValue());
        break;
      case "mode":
        mViewManager.setMode(view, (String) value);
        break;
      case "timeZoneOffsetInMinutes":
        mViewManager.setTimeZoneOffsetInMinutes(view, value == null ? null : (String) value);
        break;
      case "textColor":
        mViewManager.setTextColor(view, value == null ? null : (String) value);
        break;
      case "dividerColor":
        mViewManager.setDividerColor(view, value == null ? null : (String) value);
        break;
      case "buttonColor":
        mViewManager.setButtonColor(view, value == null ? null : (String) value);
        break;
      case "is24hourSource":
        mViewManager.setIs24hourSource(view, (String) value);
        break;
      case "theme":
        mViewManager.setTheme(view, (String) value);
        break;
      case "modal":
        mViewManager.setModal(view, value == null ? false : (boolean) value);
        break;
      case "open":
        mViewManager.setOpen(view, value == null ? false : (boolean) value);
        break;
      case "confirmText":
        mViewManager.setConfirmText(view, value == null ? null : (String) value);
        break;
      case "cancelText":
        mViewManager.setCancelText(view, value == null ? null : (String) value);
        break;
      case "title":
        mViewManager.setTitle(view, value == null ? null : (String) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
