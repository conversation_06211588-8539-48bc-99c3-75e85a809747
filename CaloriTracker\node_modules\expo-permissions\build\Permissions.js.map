{"version": 3, "file": "Permissions.js", "sourceRoot": "", "sources": ["../src/Permissions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,EACL,mBAAmB,EACnB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,GAChB,MAAM,wBAAwB,CAAC;AAChC,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAC5C,OAAO,EAIL,gBAAgB,GAGjB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EACL,gBAAgB,GAMjB,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC;AAC/B,MAAM,CAAC,MAAM,aAAa,GAAG,cAAc,CAAC;AAC5C,MAAM,CAAC,MAAM,wBAAwB,GAAG,uBAAuB,CAAC;AAChE;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,aAAa,CAAC;AACzC,MAAM,CAAC,MAAM,eAAe,GAAG,gBAAgB,CAAC;AAChD,6EAA6E;AAC7E,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC;AACnC,MAAM,CAAC,MAAM,mBAAmB,GAAG,oBAAoB,CAAC;AACxD,MAAM,CAAC,MAAM,mBAAmB,GAAG,oBAAoB,CAAC;AACxD,MAAM,CAAC,MAAM,yBAAyB,GAAG,yBAAyB,CAAC;AACnE,MAAM,CAAC,MAAM,aAAa,GAAG,eAAe,CAAC;AAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC;AACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC;AACnC,MAAM,CAAC,MAAM,SAAS,GAAG,WAAW,CAAC;AACrC,MAAM,CAAC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC;AACpD,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC;AAE/B,kDAAkD;AAClD,MAAM,yBAAyB,GAAG;IAChC,CAAC,MAAM,CAAC,EAAE,aAAa;IACvB,CAAC,aAAa,CAAC,EAAE,oBAAoB;IACrC,CAAC,wBAAwB,CAAC,EAAE,oBAAoB;IAChD,CAAC,eAAe,CAAC,EAAE,SAAS;IAC5B,CAAC,QAAQ,CAAC,EAAE,eAAe;IAC3B,CAAC,yBAAyB,CAAC,EAAE,oBAAoB;IACjD,CAAC,aAAa,CAAC,EAAE,oBAAoB;IACrC,CAAC,QAAQ,CAAC,EAAE,eAAe;IAC3B,CAAC,QAAQ,CAAC,EAAE,eAAe;IAC3B,CAAC,SAAS,CAAC,EAAE,eAAe;IAC5B,CAAC,iBAAiB,CAAC,EAAE,iBAAiB;IACtC,CAAC,MAAM,CAAC,EAAE,cAAc;CACzB,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,QAAQ,CAAC,GAAG,KAAuB;IACvD,OAAO,CAAC,IAAI,CACV,2NAA2N,CAC5N,CAAC;IAEF,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,OAAO,MAAM,sCAAsC,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;KAClF;IACD,OAAO,MAAM,8BAA8B,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC3E,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ,CAAC,GAAG,KAAuB;IACvD,OAAO,CAAC,IAAI,CACV,2NAA2N,CAC5N,CAAC;IAEF,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,OAAO,MAAM,sCAAsC,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;KAClF;IACD,OAAO,MAAM,8BAA8B,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC3E,CAAC;AAED,KAAK,UAAU,sCAAsC,CACnD,IAAoB,EACpB,gBAAmE;IAEnE,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,EAAE;QAC9C,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,IAAI;SAClB,CAAC;KACH;IACD,IAAI;QACF,OAAO,MAAM,gBAAgB,CAAC,IAAI,CAAC,CAAC;KACrC;IAAC,OAAO,KAAK,EAAE;QACd,+GAA+G;QAC/G,IAAI,KAAK,CAAC,IAAI,KAAK,uBAAuB,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE;YAC7E,MAAM,OAAO,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;YAChD,KAAK,CAAC,OAAO,GAAG,GAAG,KAAK,CAAC,OAAO,yCAAyC,yBAAyB,CAAC,IAAI,CAAC,iEAAiE,OAAO,EAAE,CAAC;SACpL;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,KAAK,UAAU,sCAAsC,CACnD,KAAuB,EACvB,gBAAmE;IAEnE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;KACnE;IAED,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,sCAAsC,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;KAC1F;IAED,OAAO;QACL,MAAM,EAAE,gBAAgB,CAAC,WAAW,CAAC;QACrC,OAAO,EAAE,mBAAmB,CAAC,WAAW,CAAC;QACzC,WAAW,EAAE,mBAAmB,CAAC,WAAW,CAAC;QAC7C,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC;QACrC,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,8BAA8B,CAC3C,KAAuB,EACvB,iBAAsE;IAEtE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;KACnE;IAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACxE,MAAM,kBAAkB,GAAG;YACzB,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,IAAI;SAClB,CAAC;QACF,OAAO;YACL,GAAG,kBAAkB;YACrB,aAAa;YACb,WAAW,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;SAC5C,CAAC;KACH;IAED,MAAM,WAAW,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACnD,OAAO;QACL,MAAM,EAAE,gBAAgB,CAAC,WAAW,CAAC;QACrC,OAAO,EAAE,mBAAmB,CAAC,WAAW,CAAC;QACzC,WAAW,EAAE,mBAAmB,CAAC,WAAW,CAAC;QAC7C,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC;QACrC,WAAW;KACZ,CAAC;AACJ,CAAC", "sourcesContent": ["import { Platform } from 'react-native';\n\nimport {\n  coalesceExpirations,\n  coalesceStatuses,\n  coalesceCanAskAgain,\n  coalesceGranted,\n} from './CoalescedPermissions';\nimport Permissions from './ExpoPermissions';\nimport {\n  PermissionResponse,\n  PermissionType,\n  PermissionMap,\n  PermissionStatus,\n  PermissionExpiration,\n  PermissionInfo,\n} from './Permissions.types';\n\nexport {\n  PermissionStatus,\n  PermissionResponse,\n  PermissionExpiration,\n  PermissionMap,\n  PermissionInfo,\n  PermissionType,\n};\n\nexport const CAMERA = 'camera';\nexport const MEDIA_LIBRARY = 'mediaLibrary';\nexport const MEDIA_LIBRARY_WRITE_ONLY = 'mediaLibraryWriteOnly';\n/**\n * @deprecated Use `MEDIA_LIBRARY` or `MEDIA_LIBRARY_WRITE_ONLY`\n */\nexport const CAMERA_ROLL = MEDIA_LIBRARY;\nexport const AUDIO_RECORDING = 'audioRecording';\n/** @deprecated Use `LOCATION_FOREGROUND` or `LOCATION_BACKGROUND` instead */\nexport const LOCATION = 'location';\nexport const LOCATION_FOREGROUND = 'locationForeground';\nexport const LOCATION_BACKGROUND = 'locationBackground';\nexport const USER_FACING_NOTIFICATIONS = 'userFacingNotifications';\nexport const NOTIFICATIONS = 'notifications';\nexport const CONTACTS = 'contacts';\nexport const CALENDAR = 'calendar';\nexport const REMINDERS = 'reminders';\nexport const SYSTEM_BRIGHTNESS = 'systemBrightness';\nexport const MOTION = 'motion';\n\n// Map corresponding permission to correct package\nconst PERMISSION_MODULE_MAPPING = {\n  [CAMERA]: 'expo-camera',\n  [MEDIA_LIBRARY]: 'expo-media-library',\n  [MEDIA_LIBRARY_WRITE_ONLY]: 'expo-media-library',\n  [AUDIO_RECORDING]: 'expo-av',\n  [LOCATION]: 'expo-location',\n  [USER_FACING_NOTIFICATIONS]: 'expo-notifications',\n  [NOTIFICATIONS]: 'expo-notifications',\n  [CONTACTS]: 'expo-contacts',\n  [CALENDAR]: 'expo-calendar',\n  [REMINDERS]: 'expo-calendar',\n  [SYSTEM_BRIGHTNESS]: 'expo-brightness',\n  [MOTION]: 'expo-sensors',\n};\n\nexport async function getAsync(...types: PermissionType[]): Promise<PermissionResponse> {\n  console.warn(\n    `expo-permissions is now deprecated — the functionality has been moved to other expo packages that directly use these permissions (e.g. expo-location, expo-camera). The package will be removed in the upcoming releases.`\n  );\n\n  if (Platform.OS === 'ios') {\n    return await _handleMultiPermissionsRequestIOSAsync(types, Permissions.getAsync);\n  }\n  return await _handlePermissionsRequestAsync(types, Permissions.getAsync);\n}\n\nexport async function askAsync(...types: PermissionType[]): Promise<PermissionResponse> {\n  console.warn(\n    `expo-permissions is now deprecated — the functionality has been moved to other expo packages that directly use these permissions (e.g. expo-location, expo-camera). The package will be removed in the upcoming releases.`\n  );\n\n  if (Platform.OS === 'ios') {\n    return await _handleMultiPermissionsRequestIOSAsync(types, Permissions.askAsync);\n  }\n  return await _handlePermissionsRequestAsync(types, Permissions.askAsync);\n}\n\nasync function _handleSinglePermissionRequestIOSAsync(\n  type: PermissionType,\n  handlePermission: (type: PermissionType) => Promise<PermissionInfo>\n): Promise<PermissionInfo> {\n  if (Platform.OS !== 'web' && type === 'motion') {\n    return {\n      status: PermissionStatus.GRANTED,\n      expires: 'never',\n      granted: true,\n      canAskAgain: true,\n    };\n  }\n  try {\n    return await handlePermission(type);\n  } catch (error) {\n    // We recognize the permission's library, so we inform the user to link that library to request the permission.\n    if (error.code === 'E_PERMISSIONS_UNKNOWN' && PERMISSION_MODULE_MAPPING[type]) {\n      const library = PERMISSION_MODULE_MAPPING[type];\n      error.message = `${error.message}, please install and link the package ${PERMISSION_MODULE_MAPPING[type]}, see more at https://github.com/expo/expo/tree/main/packages/${library}`;\n    }\n    throw error;\n  }\n}\n\nasync function _handleMultiPermissionsRequestIOSAsync(\n  types: PermissionType[],\n  handlePermission: (type: PermissionType) => Promise<PermissionInfo>\n): Promise<PermissionResponse> {\n  if (!types.length) {\n    throw new Error('At least one permission type must be specified');\n  }\n\n  const permissions = {};\n  for (const type of types) {\n    permissions[type] = await _handleSinglePermissionRequestIOSAsync(type, handlePermission);\n  }\n\n  return {\n    status: coalesceStatuses(permissions),\n    expires: coalesceExpirations(permissions),\n    canAskAgain: coalesceCanAskAgain(permissions),\n    granted: coalesceGranted(permissions),\n    permissions,\n  };\n}\n\nasync function _handlePermissionsRequestAsync(\n  types: PermissionType[],\n  handlePermissions: (types: PermissionType[]) => Promise<PermissionMap>\n): Promise<PermissionResponse> {\n  if (!types.length) {\n    throw new Error('At least one permission type must be specified');\n  }\n\n  if (Platform.OS !== 'web' && types.length === 1 && types[0] === 'motion') {\n    const approvedPermission = {\n      status: PermissionStatus.GRANTED,\n      expires: 'never',\n      granted: true,\n      canAskAgain: true,\n    };\n    return {\n      ...approvedPermission,\n      // @ts-ignore\n      permissions: { motion: approvedPermission },\n    };\n  }\n\n  const permissions = await handlePermissions(types);\n  return {\n    status: coalesceStatuses(permissions),\n    expires: coalesceExpirations(permissions),\n    canAskAgain: coalesceCanAskAgain(permissions),\n    granted: coalesceGranted(permissions),\n    permissions,\n  };\n}\n"]}