import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  Button,
  Input,
  NumberInput,
  ButtonGroup,
  theme,
  commonStyles,
} from '../components';

import DataService from '../services/dataService';
import { UserProfile } from '../models';

/**
 * Profile Screen - User profile management and settings
 */
const ProfileScreen = ({ navigation }) => {
  const [profile, setProfile] = useState(new UserProfile());
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const genderOptions = ['Male', 'Female', 'Other'];
  const activityLevels = ['Sedentary', 'Light', 'Moderate', 'Active', 'Very Active'];
  const goalOptions = ['Lose Weight', 'Maintain', 'Gain Weight'];

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const userProfile = await DataService.getUserProfile();
      setProfile(userProfile);
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Validate required fields
      if (!profile.name.trim()) {
        Alert.alert('Validation Error', 'Please enter your name.');
        return;
      }

      const success = await DataService.saveUserProfile(profile);
      
      if (success) {
        setEditing(false);
        Alert.alert('Success', 'Profile updated successfully!');
      } else {
        Alert.alert('Error', 'Failed to save profile. Please try again.');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditing(false);
    loadProfile(); // Reload original data
  };

  const updateProfile = (field, value) => {
    setProfile(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const renderProfileInfo = () => {
    if (editing) {
      return (
        <View style={styles.editContainer}>
          <Input
            label="Name"
            value={profile.name}
            onChangeText={(value) => updateProfile('name', value)}
            placeholder="Enter your name"
            required
          />

          <NumberInput
            label="Age"
            value={profile.age?.toString() || ''}
            onChangeText={(value) => updateProfile('age', parseInt(value) || null)}
            placeholder="Enter your age"
            min={1}
            max={120}
            unit="years"
          />

          <View style={styles.genderContainer}>
            <Text style={styles.label}>Gender</Text>
            <ButtonGroup
              buttons={genderOptions}
              selectedIndex={genderOptions.findIndex(g => g.toLowerCase() === profile.gender)}
              onPress={(index) => updateProfile('gender', genderOptions[index].toLowerCase())}
            />
          </View>

          <NumberInput
            label="Height"
            value={profile.height?.toString() || ''}
            onChangeText={(value) => updateProfile('height', parseInt(value) || null)}
            placeholder="Enter your height"
            min={100}
            max={250}
            unit="cm"
          />

          <NumberInput
            label="Weight"
            value={profile.weight?.toString() || ''}
            onChangeText={(value) => updateProfile('weight', parseFloat(value) || null)}
            placeholder="Enter your weight"
            min={30}
            max={300}
            step={0.1}
            unit="kg"
          />

          <View style={styles.activityContainer}>
            <Text style={styles.label}>Activity Level</Text>
            <ButtonGroup
              buttons={activityLevels}
              selectedIndex={activityLevels.findIndex(a => 
                a.toLowerCase().replace(' ', '_') === profile.activityLevel
              )}
              onPress={(index) => 
                updateProfile('activityLevel', activityLevels[index].toLowerCase().replace(' ', '_'))
              }
            />
          </View>

          <View style={styles.goalContainer}>
            <Text style={styles.label}>Goal</Text>
            <ButtonGroup
              buttons={goalOptions}
              selectedIndex={goalOptions.findIndex(g => 
                g.toLowerCase().replace(' ', '') === profile.goal.replace('_', '')
              )}
              onPress={(index) => {
                const goalMap = ['lose', 'maintain', 'gain'];
                updateProfile('goal', goalMap[index]);
              }}
            />
          </View>
        </View>
      );
    }

    return (
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Name</Text>
          <Text style={styles.infoValue}>{profile.name || 'Not set'}</Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Age</Text>
          <Text style={styles.infoValue}>
            {profile.age ? `${profile.age} years` : 'Not set'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Gender</Text>
          <Text style={styles.infoValue}>
            {profile.gender ? profile.gender.charAt(0).toUpperCase() + profile.gender.slice(1) : 'Not set'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Height</Text>
          <Text style={styles.infoValue}>
            {profile.height ? `${profile.height} cm` : 'Not set'}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Weight</Text>
          <Text style={styles.infoValue}>
            {profile.weight ? `${profile.weight} kg` : 'Not set'}
          </Text>
        </View>

        {profile.height && profile.weight && (
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>BMI</Text>
            <Text style={styles.infoValue}>
              {profile.calculateBMI()} ({profile.getBMICategory()})
            </Text>
          </View>
        )}

        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Activity Level</Text>
          <Text style={styles.infoValue}>
            {profile.activityLevel.charAt(0).toUpperCase() + 
             profile.activityLevel.slice(1).replace('_', ' ')}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Goal</Text>
          <Text style={styles.infoValue}>
            {profile.goal.charAt(0).toUpperCase() + profile.goal.slice(1)} weight
          </Text>
        </View>
      </View>
    );
  };

  const renderActions = () => {
    if (editing) {
      return (
        <View style={styles.actionsContainer}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={handleCancel}
            style={styles.actionButton}
          />
          <Button
            title="Save"
            onPress={handleSave}
            loading={saving}
            disabled={saving}
            style={styles.actionButton}
          />
        </View>
      );
    }

    return (
      <View style={styles.actionsContainer}>
        <Button
          title="Edit Profile"
          onPress={() => setEditing(true)}
          style={styles.actionButton}
        />
      </View>
    );
  };

  const renderMenuItems = () => (
    <View style={styles.menuContainer}>
      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => navigation.navigate('Goals')}
      >
        <Text style={styles.menuItemIcon}>🎯</Text>
        <Text style={styles.menuItemText}>Daily Goals</Text>
        <Text style={styles.menuItemArrow}>›</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => navigation.navigate('Settings')}
      >
        <Text style={styles.menuItemIcon}>⚙️</Text>
        <Text style={styles.menuItemText}>Settings</Text>
        <Text style={styles.menuItemArrow}>›</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => {
          Alert.alert(
            'Export Data',
            'This feature will be available soon.',
            [{ text: 'OK' }]
          );
        }}
      >
        <Text style={styles.menuItemIcon}>📤</Text>
        <Text style={styles.menuItemText}>Export Data</Text>
        <Text style={styles.menuItemArrow}>›</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => {
          Alert.alert(
            'About CaloriTracker',
            'Version 1.0.0\n\nA smart nutrition tracking app powered by AI.',
            [{ text: 'OK' }]
          );
        }}
      >
        <Text style={styles.menuItemIcon}>ℹ️</Text>
        <Text style={styles.menuItemText}>About</Text>
        <Text style={styles.menuItemArrow}>›</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Loading profile...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.profileCard}>
          <Text style={styles.cardTitle}>Profile Information</Text>
          {renderProfileInfo()}
          {renderActions()}
        </View>

        {!editing && renderMenuItems()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  contentContainer: {
    padding: theme.spacing.md,
  },
  
  profileCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.md,
  },
  
  cardTitle: {
    ...theme.typography.h5,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  
  editContainer: {
    marginBottom: theme.spacing.md,
  },
  
  infoContainer: {
    marginBottom: theme.spacing.md,
  },
  
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  
  infoLabel: {
    ...theme.typography.body1,
    color: theme.colors.gray700,
    fontWeight: '600',
  },
  
  infoValue: {
    ...theme.typography.body1,
    color: theme.colors.onSurface,
  },
  
  label: {
    ...theme.typography.body2,
    color: theme.colors.gray700,
    marginBottom: theme.spacing.sm,
    fontWeight: '600',
  },
  
  genderContainer: {
    marginBottom: theme.spacing.md,
  },
  
  activityContainer: {
    marginBottom: theme.spacing.md,
  },
  
  goalContainer: {
    marginBottom: theme.spacing.md,
  },
  
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: theme.spacing.sm,
  },
  
  actionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  
  menuContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.md,
  },
  
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  
  menuItemIcon: {
    fontSize: 24,
    marginRight: theme.spacing.md,
  },
  
  menuItemText: {
    ...theme.typography.body1,
    color: theme.colors.onSurface,
    flex: 1,
  },
  
  menuItemArrow: {
    ...theme.typography.h5,
    color: theme.colors.gray400,
  },
});

export default ProfileScreen;
