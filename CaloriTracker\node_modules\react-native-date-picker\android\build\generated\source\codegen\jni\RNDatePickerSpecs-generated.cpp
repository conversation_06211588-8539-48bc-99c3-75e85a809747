
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "RNDatePickerSpecs.h"

namespace facebook::react {



static facebook::jsi::Value __hostFunction_NativeRNDatePickerSpecJSI_closePicker(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "closePicker", "()V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeRNDatePickerSpecJSI_openPicker(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "openPicker", "(Lcom/facebook/react/bridge/ReadableMap;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeRNDatePickerSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "removeListeners", "(D)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeRNDatePickerSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "addListener", "(Ljava/lang/String;)V", args, count, cachedMethodId);
}

NativeRNDatePickerSpecJSI::NativeRNDatePickerSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["closePicker"] = MethodMetadata {0, __hostFunction_NativeRNDatePickerSpecJSI_closePicker};
  methodMap_["openPicker"] = MethodMetadata {1, __hostFunction_NativeRNDatePickerSpecJSI_openPicker};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeRNDatePickerSpecJSI_removeListeners};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeRNDatePickerSpecJSI_addListener};
}

std::shared_ptr<TurboModule> RNDatePickerSpecs_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "RNDatePicker") {
    return std::make_shared<NativeRNDatePickerSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
