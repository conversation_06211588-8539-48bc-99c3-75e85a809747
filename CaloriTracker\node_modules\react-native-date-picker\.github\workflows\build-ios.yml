name: Build

on:
  workflow_call:
  workflow_dispatch:

env:
  APP_PATH: ./examples/Rn071/ios/DerivedData/Build/Products/Release-iphonesimulator/Rn071.app
  PROJECT_NAME: Rn071
  EXAMPLE_SRC: ./examples/Rn071/src/**

jobs:
  build-ios:
    name: iOS
    runs-on: macos-14

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - uses: actions/cache@v4
        id: cache
        with:
          path: |
            ${{ env.APP_PATH }}
            ${{ env.APP_PATH }}.dSYM
          key: ${{ runner.os }}-${{ hashFiles('src/**', 'ios/**', env.EXAMPLE_SRC, env.APP_PATH ) }}

      - uses: actions/setup-node@v4
        if: steps.cache.outputs.cache-hit != 'true'
        with:
          node-version: 18
          cache: 'yarn'

      - name: Install npm dependencies (example project)
        if: steps.cache.outputs.cache-hit != 'true'
        working-directory: ./examples/${{ env.PROJECT_NAME }}/
        run: yarn install --frozen-lockfile

      - name: Install npm dependencies (root)
        if: steps.cache.outputs.cache-hit != 'true'
        working-directory: ./
        run: yarn install --frozen-lockfile

      - name: Setup Ruby
        if: steps.cache.outputs.cache-hit != 'true'
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          working-directory: ./examples/${{ env.PROJECT_NAME }}/

      - name: setup-cocoapods
        if: steps.cache.outputs.cache-hit != 'true'
        uses: maxim-lobanov/setup-cocoapods@v1
        with:
          version: 1.11.3

      - name: Install CocoaPods
        if: steps.cache.outputs.cache-hit != 'true'
        working-directory: ./examples/${{ env.PROJECT_NAME }}/
        run: |
          yarn pods

      - name: Select Xcode
        if: steps.cache.outputs.cache-hit != 'true'
        run: sudo xcode-select -s /Applications/Xcode_$(<.xcode-version).app/Contents/Developer

      - name: Build
        if: steps.cache.outputs.cache-hit != 'true'
        working-directory: ./examples/${{ env.PROJECT_NAME }}/ios/
        run: |
          set -o pipefail && xcodebuild \
            -workspace "${{ env.PROJECT_NAME }}.xcworkspace" \
            -scheme "${{ env.PROJECT_NAME }}" \
            -destination "platform=iOS Simulator,name=iPhone 14,OS=16.4" \
            -derivedDataPath "DerivedData" \
            -configuration "Release" \
          | tee xcodebuild.log
