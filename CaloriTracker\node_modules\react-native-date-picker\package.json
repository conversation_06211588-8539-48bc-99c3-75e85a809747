{"name": "react-native-date-picker", "version": "5.0.13", "description": "A datetime picker for React Native. In-modal or inlined. Supports Android and iOS.", "source": "src/index", "main": "src/index.js", "typings": "index.d.ts", "scripts": {"prepublishOnly": "mv README.md githubREADME.md && mv npmREADME.md README.md", "postpublish": "mv README.md npmREADME.md && mv githubREADME.md README.md", "test": "jest src", "install:release": "adb install examples/Rn069/android/app/build/outputs/apk/release/app-release.apk", "install:debug": "adb install examples/Rn069/android/app/build/outputs/apk/debug/app-debug.apk", "build:release": "(cd examples/Rn069/android && ./gradlew assembleRelease) && yarn install:release", "build:debug": "(cd examples/Rn069/android && ./gradlew assembleDebug) && yarn install:debug", "number-picker:generate": "bash ./scripts/generate-number-picker.sh", "number-picker:update-patch": "bash ./scripts/update-patch.sh", "emulator": "bash ./scripts/start-android-emulator.sh", "maestro:ios": "maestro test --include-tags=ios .maestro/", "maestro:android": "maestro test --include-tags=android .maestro/", "typecheck": "yarn tsc", "lint": "yarn eslint ./src"}, "repository": {"type": "git", "url": "**************:henninghall/react-native-date-picker.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/henninghall/react-native-date-picker", "keywords": ["datepicker", "date-picker", "date picker", "datepicker", "react native", "react native date picker", "react native datepicker", "react native datetimepicker", "react native date time picker", "react-native", "react-native-date-picker"], "dependencies": {}, "peerDependencies": {"react": ">= 17.0.1", "react-native": ">= 0.64.3"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@react-native/eslint-config": "0.73.2", "@types/jest": "^29.5.12", "@types/react": "18.3.3", "babel-jest": "^26.6.3", "eslint": "^8.19.0", "eslint-plugin-ft-flow": "^3.0.9", "jest": "^26.6.3", "prettier": "2.8.8", "react-native": "0.74.1", "typescript": "5.4.5"}, "codegenConfig": {"name": "RNDatePickerSpecs", "type": "all", "jsSrcsDir": "src/fabric", "android": {"javaPackageName": "com.henninghall.date_picker"}, "ios": {"componentProvider": {"RNDatePicker": "RNDatePicker"}, "modulesProvider": {"RNDatePicker": "RNDatePickerManager"}}}}