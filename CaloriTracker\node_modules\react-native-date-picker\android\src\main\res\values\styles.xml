<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="DatePickerBaseTheme">
        <item name="android:textSize" tools:ignore="SpUsage">17sp</item>
    </style>
    <style name="DatePickerTheme" parent="DatePickerBaseTheme">
        <!-- Empty to allow overriding theme in consuming apps  -->
    </style>
    <style name="android_native">
        <item name="android:layout_marginLeft">5dp</item>
        <item name="android:layout_marginRight">5dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:descendantFocusability">blocksDescendants</item>

    </style>
    <style name="android_native_small" parent="android_native">
        <item name="android:layout_width">40dp</item>
    </style>
</resources>