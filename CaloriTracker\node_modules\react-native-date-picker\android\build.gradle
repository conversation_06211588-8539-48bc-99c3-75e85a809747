apply plugin: 'com.android.library'

def isNewArchitectureEnabled() {
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
    apply plugin: 'com.facebook.react'
}

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

def supportsNamespace() {
  def parsed = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION.tokenize('.')
  def major = parsed[0].toInteger()
  def minor = parsed[1].toInteger()

  // Namespace support was added in 7.3.0
  if (major == 7 && minor >= 3) {
    return true
  }

  return major >= 8
}

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 29)
    buildToolsVersion safeExtGet('buildToolsVersion', "25.0.3")
    if (supportsNamespace()) {
        namespace "com.henninghall.date_picker"
    }

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 18)
        targetSdkVersion safeExtGet('targetSdkVersion', 25)
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
    }

    sourceSets {
        main {
            if (isNewArchitectureEnabled()) {
                java.srcDirs += ['src/newarch']
            } else {
                java.srcDirs += ['src/oldarch']
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}



if (isNewArchitectureEnabled()) {
    buildscript {
        repositories {
            mavenCentral()
            google()
        }

        dependencies {
            classpath("com.android.tools.build:gradle:7.1.1")
            classpath("com.facebook.react:react-native-gradle-plugin")
            classpath("de.undercouch:gradle-download-task:5.0.1")
        }
    }
}


dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.facebook.react:react-native:+'
    implementation 'org.apache.commons:commons-lang3:3.8'
    implementation group: 'net.time4j', name: 'time4j-android', version: '4.8-2021a'
}

if (isNewArchitectureEnabled()) {
    react {
        jsRootDir = file("../src/")
        libraryName = "RNDatePicker"
        codegenJavaPackageName = "com.henninghall.date_picker"
    }
}