<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res"><file name="number_picker_divider_material" path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/drawable/number_picker_divider_material.xml" qualifiers="" type="drawable"/><file name="overlay" path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/drawable/overlay.xml" qualifiers="" type="drawable"/><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-no/strings.xml" qualifiers="no"><string name="overlay">Datovelger</string><string name="year_description">Velg år</string><string name="month_description">Velg måned</string><string name="date_description">Velg dato</string><string name="day_description">Velg dag</string><string name="hour_description">Velg time</string><string name="minutes_description">Velg minutter</string><string name="ampm_description">Velg am/pm</string><string name="time_tag">Tiden er</string><string name="hour_tag">Time</string><string name="minutes_tag">Minutter</string></file><file name="number_picker_material" path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/layout/number_picker_material.xml" qualifiers="" type="layout"/><file name="native_picker" path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/layout/native_picker.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-he/strings.xml" qualifiers="he"><string name="overlay">בוחר תאריכים צף</string><string name="year_description">בחירת שנה</string><string name="month_description">בחירת חודש</string><string name="date_description">בחירת תאריך</string><string name="day_description">בחירת יום</string><string name="hour_description">בחירת שעה</string><string name="minutes_description">בחירת דקות</string><string name="ampm_description">בחירה ב־AM/PM</string><string name="time_tag">השעה היא</string><string name="hour_tag">שעה :</string><string name="minutes_tag">דקות </string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-nn/strings.xml" qualifiers="nn"><string name="overlay">Datoveljar</string><string name="year_description">Vel år</string><string name="month_description">Vel månad</string><string name="date_description">Vel dato</string><string name="day_description">Vel dag</string><string name="hour_description">Vel time</string><string name="minutes_description">Vel minutt</string><string name="ampm_description">Vel am/pm</string><string name="time_tag">Tida er</string><string name="hour_tag">Time</string><string name="minutes_tag">Minutt</string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values/styles.xml" qualifiers=""><style name="DatePickerBaseTheme">
        <item name="android:textSize" ns1:ignore="SpUsage">17sp</item>
    </style><style name="DatePickerTheme" parent="DatePickerBaseTheme">
        
    </style><style name="android_native">
        <item name="android:layout_marginLeft">5dp</item>
        <item name="android:layout_marginRight">5dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:descendantFocusability">blocksDescendants</item>

    </style><style name="android_native_small" parent="android_native">
        <item name="android:layout_width">40dp</item>
    </style></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values/strings.xml" qualifiers=""><string name="overlay">Date Picker Overlay</string><string name="year_description">Select Year</string><string name="month_description">Select Month</string><string name="date_description">Select Date</string><string name="day_description">Select Day</string><string name="hour_description">Select Hour</string><string name="minutes_description">Select Minutes</string><string name="ampm_description">Select AM/PM</string><string name="time_tag">Time is</string><string name="hour_tag">Hour :</string><string name="minutes_tag">Minutes </string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values/attrs.xml" qualifiers=""><declare-styleable name="Theme">
        
        <attr format="reference" name="numberPickerStyle"/>
    </declare-styleable><declare-styleable name="NumberPicker">
        
        <attr format="color|reference" name="solidColor"/>
        
        <attr format="reference" name="selectionDivider"/>
        
        <attr format="dimension" name="selectionDividerHeight"/>
        
        <attr format="dimension" name="selectionDividersDistance"/>
        
        <attr format="dimension" name="internalMinHeight"/>
        
        <attr format="dimension" name="internalMaxHeight"/>
        
        <attr format="dimension" name="internalMinWidth"/>
        
        <attr format="dimension" name="internalMaxWidth"/>
        
        <attr name="internalLayout"/>
        
        <attr format="reference" name="virtualButtonPressedDrawable"/>
        
        <attr format="boolean" name="hideWheelUntilFocused"/>
    </declare-styleable></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-sv/strings.xml" qualifiers="sv"><string name="overlay">Datumväljare på</string><string name="year_description">Välj ett år</string><string name="month_description">Välj en månad</string><string name="date_description">Välj ett datum</string><string name="day_description">Välj en dag</string><string name="hour_description">Välj en timme</string><string name="minutes_description">Välj minuter</string><string name="ampm_description">Välj am/pm</string><string name="time_tag">Tiden är</string><string name="hour_tag">Timme:</string><string name="minutes_tag">Minuter</string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-fi/strings.xml" qualifiers="fi"><string name="overlay">Päivämääränvalitsin</string><string name="year_description">Valitse Vuosi</string><string name="month_description">Valitse Kuukausi</string><string name="date_description">Valitse Päivämäärä</string><string name="day_description">Valitse Päivä</string><string name="hour_description">Valitse Tunti</string><string name="minutes_description">Valitse Minuutit</string><string name="ampm_description">Valitse am/pm</string><string name="time_tag">Kello on</string><string name="hour_tag">Tunti:</string><string name="minutes_tag">Minuutit</string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-fr/strings.xml" qualifiers="fr"><string name="overlay">Date Picker Overlay</string><string name="year_description">Sélectionnez l\'année</string><string name="month_description">Sélectionnez le mois</string><string name="date_description">Sélectionnez la date</string><string name="day_description">Sélectionnez le jour</string><string name="hour_description">Sélectionnez l\'heure</string><string name="minutes_description">Sélectionnez les minutes</string><string name="ampm_description">Sélectionnez AM/PM</string><string name="time_tag">Le temps est</string><string name="hour_tag">Heure :</string><string name="minutes_tag">Minutes </string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-nl/strings.xml" qualifiers="nl"><string name="overlay">Date Picker Overlay</string><string name="year_description">Selecteer jaar</string><string name="month_description">Selecteer maand</string><string name="date_description">Selecteer datum</string><string name="day_description">Selecteer dag</string><string name="hour_description">Selecteer uur</string><string name="minutes_description">Selecteer minuten</string><string name="ampm_description">Selecteer AM/PM</string><string name="time_tag">Tijd is</string><string name="hour_tag">Uur :</string><string name="minutes_tag">Minuten </string></file><file path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/main/res/values-nb/strings.xml" qualifiers="nb"><string name="overlay">Datovelger</string><string name="year_description">Velg år</string><string name="month_description">Velg måned</string><string name="date_description">Velg dato</string><string name="day_description">Velg dag</string><string name="hour_description">Velg time</string><string name="minutes_description">Velg minutter</string><string name="ampm_description">Velg am/pm</string><string name="time_tag">Tiden er</string><string name="hour_tag">Time</string><string name="minutes_tag">Minutter</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native-date-picker/examples/Expo52/node_modules/react-native-date-picker/android/build/generated/res/resValues/debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="NumberPicker">
        
        <attr format="color|reference" name="solidColor"/>
        
        <attr format="reference" name="selectionDivider"/>
        
        <attr format="dimension" name="selectionDividerHeight"/>
        
        <attr format="dimension" name="selectionDividersDistance"/>
        
        <attr format="dimension" name="internalMinHeight"/>
        
        <attr format="dimension" name="internalMaxHeight"/>
        
        <attr format="dimension" name="internalMinWidth"/>
        
        <attr format="dimension" name="internalMaxWidth"/>
        
        <attr name="internalLayout"/>
        
        <attr format="reference" name="virtualButtonPressedDrawable"/>
        
        <attr format="boolean" name="hideWheelUntilFocused"/>
    </declare-styleable><declare-styleable name="Theme">
        
        <attr format="reference" name="numberPickerStyle"/>
    </declare-styleable></configuration></mergedItems></merger>