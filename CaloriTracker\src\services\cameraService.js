import * as ImagePicker from 'expo-image-picker';
import * as Camera from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import { Alert } from 'react-native';

/**
 * Camera Service for handling image capture and selection
 */
class CameraService {
  constructor() {
    this.cameraPermission = null;
    this.mediaLibraryPermission = null;
  }

  /**
   * Request camera permissions
   * @returns {Promise<boolean>} Permission granted status
   */
  async requestCameraPermission() {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      this.cameraPermission = status === 'granted';
      
      if (!this.cameraPermission) {
        Alert.alert(
          'Camera Permission Required',
          'Please enable camera access in your device settings to take photos of your meals.',
          [{ text: 'OK' }]
        );
      }
      
      return this.cameraPermission;
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return false;
    }
  }

  /**
   * Request media library permissions
   * @returns {Promise<boolean>} Permission granted status
   */
  async requestMediaLibraryPermission() {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      this.mediaLibraryPermission = status === 'granted';
      
      if (!this.mediaLibraryPermission) {
        Alert.alert(
          'Photo Library Permission Required',
          'Please enable photo library access to select images from your gallery.',
          [{ text: 'OK' }]
        );
      }
      
      return this.mediaLibraryPermission;
    } catch (error) {
      console.error('Error requesting media library permission:', error);
      return false;
    }
  }

  /**
   * Check if camera permissions are granted
   * @returns {Promise<boolean>} Permission status
   */
  async checkCameraPermission() {
    try {
      const { status } = await Camera.getCameraPermissionsAsync();
      this.cameraPermission = status === 'granted';
      return this.cameraPermission;
    } catch (error) {
      console.error('Error checking camera permission:', error);
      return false;
    }
  }

  /**
   * Check if media library permissions are granted
   * @returns {Promise<boolean>} Permission status
   */
  async checkMediaLibraryPermission() {
    try {
      const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
      this.mediaLibraryPermission = status === 'granted';
      return this.mediaLibraryPermission;
    } catch (error) {
      console.error('Error checking media library permission:', error);
      return false;
    }
  }

  /**
   * Take a photo using the camera
   * @param {Object} options - Camera options
   * @returns {Promise<Object>} Image result
   */
  async takePhoto(options = {}) {
    try {
      // Check and request camera permission
      const hasPermission = await this.checkCameraPermission() || await this.requestCameraPermission();
      
      if (!hasPermission) {
        return {
          success: false,
          error: 'Camera permission not granted',
          cancelled: false,
        };
      }

      const defaultOptions = {
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: false,
        exif: false,
      };

      const finalOptions = { ...defaultOptions, ...options };

      const result = await ImagePicker.launchCameraAsync(finalOptions);

      if (result.cancelled || result.canceled) {
        return {
          success: false,
          error: null,
          cancelled: true,
        };
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        return {
          success: true,
          image: {
            uri: asset.uri,
            width: asset.width,
            height: asset.height,
            type: asset.type,
            fileSize: asset.fileSize,
          },
          cancelled: false,
        };
      } else {
        return {
          success: false,
          error: 'No image captured',
          cancelled: false,
        };
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      return {
        success: false,
        error: error.message,
        cancelled: false,
      };
    }
  }

  /**
   * Select an image from the gallery
   * @param {Object} options - Image picker options
   * @returns {Promise<Object>} Image result
   */
  async selectFromGallery(options = {}) {
    try {
      // Check and request media library permission
      const hasPermission = await this.checkMediaLibraryPermission() || await this.requestMediaLibraryPermission();
      
      if (!hasPermission) {
        return {
          success: false,
          error: 'Media library permission not granted',
          cancelled: false,
        };
      }

      const defaultOptions = {
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: false,
        exif: false,
      };

      const finalOptions = { ...defaultOptions, ...options };

      const result = await ImagePicker.launchImageLibraryAsync(finalOptions);

      if (result.cancelled || result.canceled) {
        return {
          success: false,
          error: null,
          cancelled: true,
        };
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        return {
          success: true,
          image: {
            uri: asset.uri,
            width: asset.width,
            height: asset.height,
            type: asset.type,
            fileSize: asset.fileSize,
          },
          cancelled: false,
        };
      } else {
        return {
          success: false,
          error: 'No image selected',
          cancelled: false,
        };
      }
    } catch (error) {
      console.error('Error selecting from gallery:', error);
      return {
        success: false,
        error: error.message,
        cancelled: false,
      };
    }
  }

  /**
   * Show image picker options (camera or gallery)
   * @param {Object} options - Picker options
   * @returns {Promise<Object>} Image result
   */
  async showImagePicker(options = {}) {
    return new Promise((resolve) => {
      Alert.alert(
        'Select Image',
        'Choose how you want to add a photo of your meal',
        [
          {
            text: 'Camera',
            onPress: async () => {
              const result = await this.takePhoto(options);
              resolve(result);
            },
          },
          {
            text: 'Gallery',
            onPress: async () => {
              const result = await this.selectFromGallery(options);
              resolve(result);
            },
          },
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              resolve({
                success: false,
                error: null,
                cancelled: true,
              });
            },
          },
        ],
        { cancelable: true }
      );
    });
  }

  /**
   * Save image to device gallery
   * @param {string} imageUri - URI of the image to save
   * @returns {Promise<Object>} Save result
   */
  async saveToGallery(imageUri) {
    try {
      // Request media library permission
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        return {
          success: false,
          error: 'Permission to access media library denied',
        };
      }

      const asset = await MediaLibrary.createAssetAsync(imageUri);
      
      return {
        success: true,
        asset: asset,
      };
    } catch (error) {
      console.error('Error saving to gallery:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate image file
   * @param {Object} image - Image object to validate
   * @returns {Object} Validation result
   */
  validateImage(image) {
    if (!image || !image.uri) {
      return {
        isValid: false,
        error: 'No image provided',
      };
    }

    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (image.fileSize && image.fileSize > maxSize) {
      return {
        isValid: false,
        error: 'Image file is too large (max 10MB)',
      };
    }

    // Check image dimensions (minimum 100x100)
    if (image.width && image.height) {
      if (image.width < 100 || image.height < 100) {
        return {
          isValid: false,
          error: 'Image is too small (minimum 100x100 pixels)',
        };
      }
    }

    return {
      isValid: true,
      error: null,
    };
  }

  /**
   * Get image info
   * @param {string} imageUri - URI of the image
   * @returns {Promise<Object>} Image information
   */
  async getImageInfo(imageUri) {
    try {
      const info = await ImagePicker.getImageInfoAsync(imageUri);
      return {
        success: true,
        info: info,
      };
    } catch (error) {
      console.error('Error getting image info:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

export default new CameraService();
