/**
 * Tests for Camera Service
 */

import CameraService from '../cameraService';
import * as ImagePicker from 'expo-image-picker';
import * as Camera from 'expo-camera';
import { Alert } from 'react-native';

// Mock the expo modules
jest.mock('expo-image-picker');
jest.mock('expo-camera');
jest.mock('expo-media-library');
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
}));

describe('CameraService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requestCameraPermission', () => {
    it('should request camera permission successfully', async () => {
      Camera.requestCameraPermissionsAsync.mockResolvedValue({ status: 'granted' });

      const result = await CameraService.requestCameraPermission();

      expect(result).toBe(true);
      expect(Camera.requestCameraPermissionsAsync).toHaveBeenCalled();
    });

    it('should handle camera permission denial', async () => {
      Camera.requestCameraPermissionsAsync.mockResolvedValue({ status: 'denied' });

      const result = await CameraService.requestCameraPermission();

      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith(
        'Camera Permission Required',
        expect.any(String),
        expect.any(Array)
      );
    });

    it('should handle camera permission request error', async () => {
      Camera.requestCameraPermissionsAsync.mockRejectedValue(new Error('Permission error'));

      const result = await CameraService.requestCameraPermission();

      expect(result).toBe(false);
    });
  });

  describe('requestMediaLibraryPermission', () => {
    it('should request media library permission successfully', async () => {
      ImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({ status: 'granted' });

      const result = await CameraService.requestMediaLibraryPermission();

      expect(result).toBe(true);
      expect(ImagePicker.requestMediaLibraryPermissionsAsync).toHaveBeenCalled();
    });

    it('should handle media library permission denial', async () => {
      ImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({ status: 'denied' });

      const result = await CameraService.requestMediaLibraryPermission();

      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith(
        'Photo Library Permission Required',
        expect.any(String),
        expect.any(Array)
      );
    });
  });

  describe('takePhoto', () => {
    it('should take photo successfully', async () => {
      Camera.getCameraPermissionsAsync.mockResolvedValue({ status: 'granted' });
      ImagePicker.launchCameraAsync.mockResolvedValue({
        cancelled: false,
        assets: [
          {
            uri: 'file://test-image.jpg',
            width: 1920,
            height: 1080,
            type: 'image',
            fileSize: 1024000,
          },
        ],
      });

      const result = await CameraService.takePhoto();

      expect(result.success).toBe(true);
      expect(result.image.uri).toBe('file://test-image.jpg');
      expect(result.cancelled).toBe(false);
    });

    it('should handle cancelled photo capture', async () => {
      Camera.getCameraPermissionsAsync.mockResolvedValue({ status: 'granted' });
      ImagePicker.launchCameraAsync.mockResolvedValue({
        cancelled: true,
      });

      const result = await CameraService.takePhoto();

      expect(result.success).toBe(false);
      expect(result.cancelled).toBe(true);
    });

    it('should handle camera permission not granted', async () => {
      Camera.getCameraPermissionsAsync.mockResolvedValue({ status: 'denied' });
      Camera.requestCameraPermissionsAsync.mockResolvedValue({ status: 'denied' });

      const result = await CameraService.takePhoto();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Camera permission not granted');
    });

    it('should handle camera error', async () => {
      Camera.getCameraPermissionsAsync.mockResolvedValue({ status: 'granted' });
      ImagePicker.launchCameraAsync.mockRejectedValue(new Error('Camera error'));

      const result = await CameraService.takePhoto();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Camera error');
    });
  });

  describe('selectFromGallery', () => {
    it('should select image from gallery successfully', async () => {
      ImagePicker.getMediaLibraryPermissionsAsync.mockResolvedValue({ status: 'granted' });
      ImagePicker.launchImageLibraryAsync.mockResolvedValue({
        cancelled: false,
        assets: [
          {
            uri: 'file://gallery-image.jpg',
            width: 1920,
            height: 1080,
            type: 'image',
            fileSize: 2048000,
          },
        ],
      });

      const result = await CameraService.selectFromGallery();

      expect(result.success).toBe(true);
      expect(result.image.uri).toBe('file://gallery-image.jpg');
      expect(result.cancelled).toBe(false);
    });

    it('should handle cancelled gallery selection', async () => {
      ImagePicker.getMediaLibraryPermissionsAsync.mockResolvedValue({ status: 'granted' });
      ImagePicker.launchImageLibraryAsync.mockResolvedValue({
        cancelled: true,
      });

      const result = await CameraService.selectFromGallery();

      expect(result.success).toBe(false);
      expect(result.cancelled).toBe(true);
    });

    it('should handle media library permission not granted', async () => {
      ImagePicker.getMediaLibraryPermissionsAsync.mockResolvedValue({ status: 'denied' });
      ImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({ status: 'denied' });

      const result = await CameraService.selectFromGallery();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Media library permission not granted');
    });
  });

  describe('validateImage', () => {
    it('should validate valid image', () => {
      const image = {
        uri: 'file://test-image.jpg',
        width: 1920,
        height: 1080,
        fileSize: 1024000,
      };

      const result = CameraService.validateImage(image);

      expect(result.isValid).toBe(true);
      expect(result.error).toBe(null);
    });

    it('should reject image without URI', () => {
      const image = {
        width: 1920,
        height: 1080,
      };

      const result = CameraService.validateImage(image);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('No image provided');
    });

    it('should reject image that is too large', () => {
      const image = {
        uri: 'file://large-image.jpg',
        width: 1920,
        height: 1080,
        fileSize: 15 * 1024 * 1024, // 15MB
      };

      const result = CameraService.validateImage(image);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Image file is too large (max 10MB)');
    });

    it('should reject image that is too small', () => {
      const image = {
        uri: 'file://small-image.jpg',
        width: 50,
        height: 50,
        fileSize: 1024,
      };

      const result = CameraService.validateImage(image);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Image is too small (minimum 100x100 pixels)');
    });
  });

  describe('showImagePicker', () => {
    it('should show image picker alert', async () => {
      // Mock Alert.alert to simulate user selecting camera
      Alert.alert.mockImplementation((title, message, buttons) => {
        // Simulate user pressing "Camera" button
        buttons[0].onPress();
      });

      Camera.getCameraPermissionsAsync.mockResolvedValue({ status: 'granted' });
      ImagePicker.launchCameraAsync.mockResolvedValue({
        cancelled: false,
        assets: [
          {
            uri: 'file://camera-image.jpg',
            width: 1920,
            height: 1080,
            type: 'image',
            fileSize: 1024000,
          },
        ],
      });

      const resultPromise = CameraService.showImagePicker();

      // Wait for the promise to resolve
      const result = await resultPromise;

      expect(Alert.alert).toHaveBeenCalledWith(
        'Select Image',
        expect.any(String),
        expect.any(Array),
        expect.any(Object)
      );
    });
  });
});
