import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  Button,
  NumberInput,
  ButtonGroup,
  theme,
  commonStyles,
} from '../components';

import DataService from '../services/dataService';
import { validateNutritionGoals } from '../utils/apiUtils';

/**
 * Goals Screen - Manage daily nutrition goals
 */
const GoalsScreen = ({ navigation }) => {
  const [goals, setGoals] = useState({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fat: 65,
  });
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [useCalculated, setUseCalculated] = useState(false);

  const activityMultipliers = {
    sedentary: 1.2,
    light: 1.375,
    moderate: 1.55,
    active: 1.725,
    very_active: 1.9,
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      const [currentGoals, profile] = await Promise.all([
        DataService.getDailyGoals(),
        DataService.getUserProfile(),
      ]);

      setGoals(currentGoals);
      setUserProfile(profile);

      // Check if we should show calculated goals
      if (profile.age && profile.gender && profile.height && profile.weight) {
        const calculated = calculateGoals(profile);
        if (Math.abs(currentGoals.calories - calculated.calories) < 50) {
          setUseCalculated(true);
        }
      }
    } catch (error) {
      console.error('Error loading goals data:', error);
      Alert.alert('Error', 'Failed to load goals. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const calculateGoals = (profile) => {
    if (!profile.age || !profile.gender || !profile.height || !profile.weight) {
      return goals;
    }

    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr;
    if (profile.gender === 'male') {
      bmr = 10 * profile.weight + 6.25 * profile.height - 5 * profile.age + 5;
    } else {
      bmr = 10 * profile.weight + 6.25 * profile.height - 5 * profile.age - 161;
    }

    // Apply activity multiplier
    const activityLevel = profile.activityLevel || 'moderate';
    const tdee = bmr * activityMultipliers[activityLevel];

    // Adjust for goal
    let calories = tdee;
    if (profile.goal === 'lose') {
      calories = tdee - 500; // 1 lb per week
    } else if (profile.goal === 'gain') {
      calories = tdee + 500; // 1 lb per week
    }

    // Calculate macros (protein: 25%, carbs: 45%, fat: 30%)
    const protein = Math.round((calories * 0.25) / 4);
    const carbs = Math.round((calories * 0.45) / 4);
    const fat = Math.round((calories * 0.30) / 9);

    return {
      calories: Math.round(calories),
      protein,
      carbs,
      fat,
    };
  };

  const handleUseCalculated = () => {
    if (!userProfile.age || !userProfile.gender || !userProfile.height || !userProfile.weight) {
      Alert.alert(
        'Incomplete Profile',
        'Please complete your profile information to use calculated goals.',
        [
          { text: 'Cancel' },
          { text: 'Go to Profile', onPress: () => navigation.navigate('Profile') },
        ]
      );
      return;
    }

    const calculated = calculateGoals(userProfile);
    setGoals(calculated);
    setUseCalculated(true);
  };

  const handleSaveGoals = async () => {
    try {
      setSaving(true);

      // Validate goals
      const validation = validateNutritionGoals(goals);
      if (!validation.isValid) {
        Alert.alert('Validation Error', validation.errors.join('\n'));
        return;
      }

      const success = await DataService.saveDailyGoals(goals);

      if (success) {
        Alert.alert('Success', 'Goals saved successfully!', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
      } else {
        Alert.alert('Error', 'Failed to save goals. Please try again.');
      }
    } catch (error) {
      console.error('Error saving goals:', error);
      Alert.alert('Error', 'Failed to save goals. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const updateGoal = (field, value) => {
    setGoals(prev => ({
      ...prev,
      [field]: value,
    }));
    setUseCalculated(false);
  };

  const renderCalculatedGoals = () => {
    if (!userProfile.age || !userProfile.gender || !userProfile.height || !userProfile.weight) {
      return (
        <View style={styles.calculatedContainer}>
          <Text style={styles.calculatedTitle}>Calculated Goals</Text>
          <Text style={styles.calculatedText}>
            Complete your profile to see personalized goal recommendations.
          </Text>
          <Button
            title="Complete Profile"
            variant="outline"
            onPress={() => navigation.navigate('Profile')}
            style={styles.profileButton}
          />
        </View>
      );
    }

    const calculated = calculateGoals(userProfile);

    return (
      <View style={styles.calculatedContainer}>
        <Text style={styles.calculatedTitle}>Recommended Goals</Text>
        <Text style={styles.calculatedSubtitle}>
          Based on your profile and {userProfile.goal} goal
        </Text>

        <View style={styles.calculatedGoals}>
          <View style={styles.calculatedItem}>
            <Text style={styles.calculatedLabel}>Calories</Text>
            <Text style={styles.calculatedValue}>{calculated.calories}</Text>
          </View>
          <View style={styles.calculatedItem}>
            <Text style={styles.calculatedLabel}>Protein</Text>
            <Text style={styles.calculatedValue}>{calculated.protein}g</Text>
          </View>
          <View style={styles.calculatedItem}>
            <Text style={styles.calculatedLabel}>Carbs</Text>
            <Text style={styles.calculatedValue}>{calculated.carbs}g</Text>
          </View>
          <View style={styles.calculatedItem}>
            <Text style={styles.calculatedLabel}>Fat</Text>
            <Text style={styles.calculatedValue}>{calculated.fat}g</Text>
          </View>
        </View>

        <Button
          title="Use Recommended Goals"
          variant="outline"
          onPress={handleUseCalculated}
          style={styles.useCalculatedButton}
        />
      </View>
    );
  };

  const renderGoalInputs = () => {
    return (
      <View style={styles.inputsContainer}>
        <Text style={styles.sectionTitle}>Daily Nutrition Goals</Text>

        {useCalculated && (
          <View style={styles.calculatedBadge}>
            <Text style={styles.calculatedBadgeText}>
              ✓ Using recommended goals
            </Text>
          </View>
        )}

        <NumberInput
          label="Daily Calories"
          value={goals.calories.toString()}
          onChangeText={(value) => updateGoal('calories', parseInt(value) || 0)}
          min={1200}
          max={4000}
          step={50}
          unit="cal"
        />

        <NumberInput
          label="Protein"
          value={goals.protein.toString()}
          onChangeText={(value) => updateGoal('protein', parseInt(value) || 0)}
          min={50}
          max={300}
          step={5}
          unit="g"
        />

        <NumberInput
          label="Carbohydrates"
          value={goals.carbs.toString()}
          onChangeText={(value) => updateGoal('carbs', parseInt(value) || 0)}
          min={100}
          max={500}
          step={10}
          unit="g"
        />

        <NumberInput
          label="Fat"
          value={goals.fat.toString()}
          onChangeText={(value) => updateGoal('fat', parseInt(value) || 0)}
          min={30}
          max={200}
          step={5}
          unit="g"
        />
      </View>
    );
  };

  const renderMacroBreakdown = () => {
    const totalCalories = goals.calories || 1;
    const proteinCal = goals.protein * 4;
    const carbsCal = goals.carbs * 4;
    const fatCal = goals.fat * 9;
    const totalMacroCal = proteinCal + carbsCal + fatCal;

    const proteinPercent = Math.round((proteinCal / totalCalories) * 100);
    const carbsPercent = Math.round((carbsCal / totalCalories) * 100);
    const fatPercent = Math.round((fatCal / totalCalories) * 100);

    return (
      <View style={styles.breakdownContainer}>
        <Text style={styles.sectionTitle}>Macro Breakdown</Text>

        <View style={styles.breakdownItem}>
          <Text style={styles.breakdownLabel}>Protein</Text>
          <Text style={styles.breakdownValue}>
            {goals.protein}g ({proteinPercent}%)
          </Text>
          <Text style={styles.breakdownCalories}>{proteinCal} cal</Text>
        </View>

        <View style={styles.breakdownItem}>
          <Text style={styles.breakdownLabel}>Carbohydrates</Text>
          <Text style={styles.breakdownValue}>
            {goals.carbs}g ({carbsPercent}%)
          </Text>
          <Text style={styles.breakdownCalories}>{carbsCal} cal</Text>
        </View>

        <View style={styles.breakdownItem}>
          <Text style={styles.breakdownLabel}>Fat</Text>
          <Text style={styles.breakdownValue}>
            {goals.fat}g ({fatPercent}%)
          </Text>
          <Text style={styles.breakdownCalories}>{fatCal} cal</Text>
        </View>

        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>Total from Macros</Text>
          <Text style={styles.totalValue}>{totalMacroCal} cal</Text>
        </View>

        {Math.abs(totalMacroCal - totalCalories) > 50 && (
          <Text style={styles.warningText}>
            ⚠️ Macro calories don't match total calories
          </Text>
        )}
      </View>
    );
  };

  const renderActions = () => {
    return (
      <View style={styles.actionsContainer}>
        <Button
          title="Cancel"
          variant="outline"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
        />
        <Button
          title="Save Goals"
          onPress={handleSaveGoals}
          loading={saving}
          disabled={saving}
          style={styles.actionButton}
        />
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Loading goals...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        {renderCalculatedGoals()}
        {renderGoalInputs()}
        {renderMacroBreakdown()}
        {renderActions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  contentContainer: {
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
  },

  calculatedContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  calculatedTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.xs,
  },

  calculatedSubtitle: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    marginBottom: theme.spacing.md,
  },

  calculatedText: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },

  calculatedGoals: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.md,
  },

  calculatedItem: {
    alignItems: 'center',
  },

  calculatedLabel: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
    marginBottom: theme.spacing.xs,
  },

  calculatedValue: {
    ...theme.typography.h6,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },

  profileButton: {
    alignSelf: 'center',
  },

  useCalculatedButton: {
    alignSelf: 'center',
  },

  inputsContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  sectionTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.md,
  },

  calculatedBadge: {
    backgroundColor: theme.colors.success,
    borderRadius: theme.borderRadius.sm,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    alignSelf: 'flex-start',
    marginBottom: theme.spacing.md,
  },

  calculatedBadgeText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontWeight: '600',
  },

  breakdownContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },

  breakdownLabel: {
    ...theme.typography.body2,
    color: theme.colors.onSurface,
    fontWeight: '600',
    flex: 1,
  },

  breakdownValue: {
    ...theme.typography.body2,
    color: theme.colors.onSurface,
    textAlign: 'center',
    flex: 1,
  },

  breakdownCalories: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
    textAlign: 'right',
    flex: 1,
  },

  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    marginTop: theme.spacing.sm,
    borderTopWidth: 2,
    borderTopColor: theme.colors.primary,
  },

  totalLabel: {
    ...theme.typography.body1,
    color: theme.colors.onSurface,
    fontWeight: 'bold',
  },

  totalValue: {
    ...theme.typography.body1,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },

  warningText: {
    ...theme.typography.caption,
    color: theme.colors.warning,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
    fontStyle: 'italic',
  },

  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.md,
  },

  actionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
});

export default GoalsScreen;
