{"modules": {"NativeRNDatePicker": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "getConstants", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "ObjectTypeAnnotation", "properties": []}, "params": []}}, {"name": "closePicker", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": []}}, {"name": "openPicker", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "props", "optional": false, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "removeListeners", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "type", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}, {"name": "addListener", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "eventName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}]}, "moduleName": "RNDatePicker"}, "RNDatePicker": {"type": "Component", "components": {"RNDatePicker": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onChange", "optional": false, "bubblingType": "bubble", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "timestamp", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}}, {"name": "onConfirm", "optional": true, "bubblingType": "bubble", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "timestamp", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}}, {"name": "onCancel", "optional": true, "bubblingType": "bubble", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}], "props": [{"name": "locale", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "date", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "maximumDate", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "minimumDate", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "minuteInterval", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "mode", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "datetime", "options": ["date", "time", "datetime"]}}, {"name": "timeZoneOffsetInMinutes", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "textColor", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "dividerColor", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "buttonColor", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "is24hourSource", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "device", "options": ["locale", "device"]}}, {"name": "theme", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "auto", "options": ["light", "dark", "auto"]}}, {"name": "modal", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "open", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "confirmText", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "cancelText", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "title", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}], "commands": []}}}}}