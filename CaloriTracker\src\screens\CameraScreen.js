import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  Button,
  Input,
  ButtonGroup,
  theme,
  commonStyles,
} from '../components';

import CameraService from '../services/cameraService';
import DataService from '../services/dataService';
import { ensurePermission, PERMISSION_TYPES } from '../utils/permissions';

/**
 * Camera Screen - Capture food images and create meals
 */
const CameraScreen = ({ navigation }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [mealName, setMealName] = useState('');
  const [mealType, setMealType] = useState('other');
  const [analyzing, setAnalyzing] = useState(false);
  const [notes, setNotes] = useState('');

  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack', 'other'];
  const mealTypeLabels = ['Breakfast', 'Lunch', 'Dinner', 'Snack', 'Other'];

  const handleImageCapture = async () => {
    try {
      // Check permissions
      const cameraPermission = await ensurePermission(PERMISSION_TYPES.CAMERA);
      const mediaPermission = await ensurePermission(PERMISSION_TYPES.MEDIA_LIBRARY);
      
      if (!cameraPermission || !mediaPermission) {
        Alert.alert(
          'Permissions Required',
          'Camera and photo library permissions are required to capture images.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Show image picker options
      const result = await CameraService.showImagePicker({
        quality: 0.8,
        allowsEditing: true,
        aspect: [4, 3],
      });

      if (result.success && result.image) {
        setSelectedImage(result.image);
        
        // Auto-generate meal name based on time
        if (!mealName) {
          const now = new Date();
          const hour = now.getHours();
          let autoType = 'other';
          let autoName = 'Meal';
          
          if (hour >= 6 && hour < 11) {
            autoType = 'breakfast';
            autoName = 'Breakfast';
          } else if (hour >= 11 && hour < 16) {
            autoType = 'lunch';
            autoName = 'Lunch';
          } else if (hour >= 16 && hour < 22) {
            autoType = 'dinner';
            autoName = 'Dinner';
          } else {
            autoType = 'snack';
            autoName = 'Snack';
          }
          
          setMealType(autoType);
          setMealName(autoName);
        }
      } else if (result.error) {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Error', 'Failed to capture image. Please try again.');
    }
  };

  const handleAnalyzeImage = async () => {
    if (!selectedImage) {
      Alert.alert('No Image', 'Please capture or select an image first.');
      return;
    }

    try {
      setAnalyzing(true);

      const mealInfo = {
        name: mealName || 'Unnamed Meal',
        type: mealType,
        timestamp: new Date(),
        notes: notes,
      };

      const result = await DataService.createMealFromImage(selectedImage.uri, mealInfo);

      if (result.success) {
        // Navigate to analysis screen with the meal data
        navigation.navigate('Analysis', {
          meal: result.meal,
          analysisData: result.analysisData,
          isNewMeal: true,
        });
        
        // Reset form
        setSelectedImage(null);
        setMealName('');
        setMealType('other');
        setNotes('');
      } else {
        Alert.alert('Analysis Failed', result.error || 'Failed to analyze the image. Please try again.');
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      Alert.alert('Error', 'Failed to analyze the image. Please try again.');
    } finally {
      setAnalyzing(false);
    }
  };

  const handleClearImage = () => {
    setSelectedImage(null);
  };

  const renderImageSection = () => {
    if (selectedImage) {
      return (
        <View style={styles.imageContainer}>
          <Image source={{ uri: selectedImage.uri }} style={styles.selectedImage} />
          <View style={styles.imageActions}>
            <Button
              title="Retake"
              variant="outline"
              onPress={handleImageCapture}
              style={styles.imageActionButton}
            />
            <Button
              title="Clear"
              variant="text"
              onPress={handleClearImage}
              style={styles.imageActionButton}
            />
          </View>
        </View>
      );
    }

    return (
      <View style={styles.captureContainer}>
        <Text style={styles.captureIcon}>📷</Text>
        <Text style={styles.captureTitle}>Capture Your Meal</Text>
        <Text style={styles.captureText}>
          Take a photo of your food to automatically analyze its nutritional content
        </Text>
        <Button
          title="Take Photo"
          onPress={handleImageCapture}
          style={styles.captureButton}
        />
      </View>
    );
  };

  const renderMealForm = () => {
    if (!selectedImage) return null;

    return (
      <View style={styles.formContainer}>
        <Input
          label="Meal Name"
          value={mealName}
          onChangeText={setMealName}
          placeholder="Enter meal name"
          style={styles.input}
        />

        <View style={styles.mealTypeContainer}>
          <Text style={styles.mealTypeLabel}>Meal Type</Text>
          <ButtonGroup
            buttons={mealTypeLabels}
            selectedIndex={mealTypes.indexOf(mealType)}
            onPress={(index) => setMealType(mealTypes[index])}
            style={styles.mealTypeButtons}
          />
        </View>

        <Input
          label="Notes (Optional)"
          value={notes}
          onChangeText={setNotes}
          placeholder="Add any notes about this meal"
          multiline={true}
          numberOfLines={3}
          style={styles.input}
        />
      </View>
    );
  };

  const renderAnalyzeButton = () => {
    if (!selectedImage) return null;

    return (
      <View style={styles.analyzeContainer}>
        <Button
          title={analyzing ? "Analyzing..." : "Analyze Nutrition"}
          onPress={handleAnalyzeImage}
          disabled={analyzing || !mealName.trim()}
          loading={analyzing}
          style={styles.analyzeButton}
        />
        
        {analyzing && (
          <View style={styles.analyzingInfo}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
            <Text style={styles.analyzingText}>
              Using AI to identify food items and calculate nutrition...
            </Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <View style={styles.container}>
        {renderImageSection()}
        {renderMealForm()}
        {renderAnalyzeButton()}
        
        {!selectedImage && (
          <View style={styles.tipsContainer}>
            <Text style={styles.tipsTitle}>Tips for Better Analysis</Text>
            <Text style={styles.tipText}>• Ensure good lighting</Text>
            <Text style={styles.tipText}>• Include the entire meal in frame</Text>
            <Text style={styles.tipText}>• Avoid shadows and reflections</Text>
            <Text style={styles.tipText}>• Take photo from above when possible</Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  
  captureContainer: {
    ...commonStyles.centerContent,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.md,
  },
  
  captureIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.md,
  },
  
  captureTitle: {
    ...theme.typography.h4,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  
  captureText: {
    ...theme.typography.body1,
    color: theme.colors.gray600,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 24,
  },
  
  captureButton: {
    minWidth: 200,
  },
  
  imageContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.md,
  },
  
  selectedImage: {
    width: '100%',
    height: 200,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  },
  
  imageActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: theme.spacing.sm,
  },
  
  imageActionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  
  formContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.md,
  },
  
  input: {
    marginBottom: theme.spacing.md,
  },
  
  mealTypeContainer: {
    marginBottom: theme.spacing.md,
  },
  
  mealTypeLabel: {
    ...theme.typography.body2,
    color: theme.colors.gray700,
    marginBottom: theme.spacing.sm,
    fontWeight: '600',
  },
  
  mealTypeButtons: {
    marginBottom: 0,
  },
  
  analyzeContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.md,
  },
  
  analyzeButton: {
    marginBottom: theme.spacing.sm,
  },
  
  analyzingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.sm,
  },
  
  analyzingText: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    marginLeft: theme.spacing.sm,
    flex: 1,
    textAlign: 'center',
  },
  
  tipsContainer: {
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginTop: theme.spacing.lg,
  },
  
  tipsTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.sm,
  },
  
  tipText: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    marginBottom: theme.spacing.xs,
    paddingLeft: theme.spacing.sm,
  },
});

export default CameraScreen;
