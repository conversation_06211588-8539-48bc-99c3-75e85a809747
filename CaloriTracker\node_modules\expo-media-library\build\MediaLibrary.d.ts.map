{"version": 3, "file": "MediaLibrary.d.ts", "sourceRoot": "", "sources": ["../src/MediaLibrary.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,IAAI,oBAAoB,EAC1C,gBAAgB,EAChB,oBAAoB,EACpB,qBAAqB,EAGrB,iBAAiB,EAClB,MAAM,mBAAmB,CAAC;AAiB3B,MAAM,MAAM,kBAAkB,GAAG,oBAAoB,GAAG;IACtD;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC;CAC/C,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAE7D,MAAM,MAAM,cAAc,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,GAAG,aAAa,CAAC;AAErF;;;KAGK;AACL,MAAM,MAAM,eAAe,GAAG,OAAO,GAAG,OAAO,CAAC;AAEhD,MAAM,MAAM,SAAS,GACjB,SAAS,GACT,WAAW,GACX,OAAO,GACP,QAAQ,GACR,cAAc,GACd,kBAAkB,GAClB,UAAU,CAAC;AACf,MAAM,MAAM,WAAW,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;AAI3D,MAAM,MAAM,eAAe,GAAG;IAC5B,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,OAAO,EAAE,SAAS,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,OAAO,EAAE,SAAS,CAAC;IACnB,SAAS,EAAE,WAAW,CAAC;IACvB,KAAK,EAAE,OAAO,CAAC;IACf,MAAM,EAAE,QAAQ,CAAC;IACjB,YAAY,EAAE,cAAc,CAAC;IAC7B,gBAAgB,EAAE,kBAAkB,CAAC;IACrC,QAAQ,EAAE,UAAU,CAAC;CACtB,CAAC;AAGF,MAAM,MAAM,KAAK,GAAG;IAClB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IACZ;;OAEG;IACH,SAAS,EAAE,cAAc,CAAC;IAC1B;;;OAGG;IACH,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;IAC/B;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAGF,MAAM,MAAM,SAAS,GAAG,KAAK,GAAG;IAC9B;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;OAGG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;;;OAKG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;CACjC,CAAC;AAEF;;;KAGK;AACL,MAAM,MAAM,YAAY,GACpB,aAAa,GACb,KAAK,GACL,eAAe,GACf,WAAW,GACX,UAAU,GACV,YAAY,GACZ,QAAQ,GACR,WAAW,CAAC;AAGhB,MAAM,MAAM,iCAAiC,GAAG;IAC9C;;;OAGG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC;CACrC,CAAC;AAGF,MAAM,MAAM,6BAA6B,GAAG;IAC1C;;;;;;OAMG;IACH,qBAAqB,EAAE,OAAO,CAAC;IAC/B;;;OAGG;IACH,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;IACzB;;;OAGG;IACH,aAAa,CAAC,EAAE,KAAK,EAAE,CAAC;IACxB;;;;OAIG;IACH,aAAa,CAAC,EAAE,KAAK,EAAE,CAAC;CACzB,CAAC;AAGF,MAAM,MAAM,QAAQ,GAAG;IACrB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAGF,MAAM,MAAM,KAAK,GAAG;IAClB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;;OAGG;IACH,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB;;;;OAIG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,QAAQ,CAAC;IAC/B;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;CAC1B,CAAC;AAGF,MAAM,MAAM,SAAS,GAAG,OAAO,GAAG,QAAQ,GAAG,YAAY,CAAC;AAG1D,MAAM,MAAM,aAAa,GAAG;IAC1B,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B,CAAC;AAGF,MAAM,MAAM,aAAa,GAAG;IAC1B;;;OAGG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;OAGG;IACH,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB;;OAEG;IACH,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB;;;;;;;OAOG;IACH,MAAM,CAAC,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;IACrC;;;OAGG;IACH,SAAS,CAAC,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;IAC9C;;;OAGG;IACH,YAAY,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAC7B;;;OAGG;IACH,aAAa,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;CAC/B,CAAC;AAGF,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI;IACzB;;OAEG;IACH,MAAM,EAAE,CAAC,EAAE,CAAC;IACZ;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB,CAAC;AAGF,MAAM,MAAM,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC;AAGtC,MAAM,MAAM,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC;AAEtC,OAAO,EACL,gBAAgB,EAChB,oBAAoB,EACpB,oBAAoB,EACpB,qBAAqB,EACrB,iBAAiB,IAAI,YAAY,GAClC,CAAC;AAiEF;;GAEG;AACH,eAAO,MAAM,SAAS,EAAE,eAAwC,CAAC;AAGjE;;GAEG;AACH,eAAO,MAAM,MAAM,EAAE,YAAkC,CAAC;AAGxD;;;;GAIG;AACH,wBAAsB,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC,CAEzD;AAGD;;;;;;GAMG;AACH,wBAAsB,uBAAuB,CAC3C,SAAS,GAAE,OAAe,EAC1B,mBAAmB,CAAC,EAAE,kBAAkB,EAAE,GACzC,OAAO,CAAC,kBAAkB,CAAC,CAQ7B;AAGD;;;;;;GAMG;AACH,wBAAsB,mBAAmB,CACvC,SAAS,GAAE,OAAe,EAC1B,mBAAmB,CAAC,EAAE,kBAAkB,EAAE,GACzC,OAAO,CAAC,kBAAkB,CAAC,CAQ7B;AAGD;;;;;;;;GAQG;AACH,eAAO,MAAM,cAAc;gBAEX,OAAO;0BAAwB,kBAAkB,EAAE;oHAMjE,CAAC;AAGH;;;;;;;;;;;;;GAaG;AACH,wBAAsB,6BAA6B,CACjD,UAAU,GAAE,eAAe,EAAuB,GACjD,OAAO,CAAC,IAAI,CAAC,CAef;AAGD;;;;;;;;;;;;;;;GAeG;AACH,wBAAsB,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAiBzF;AAGD;;;;;;;GAOG;AACH,wBAAsB,kBAAkB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAKxE;AAGD;;;;;;;;;;;;GAYG;AACH,wBAAsB,qBAAqB,CACzC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,EAC7B,KAAK,EAAE,QAAQ,EACf,IAAI,GAAE,OAAc,GACnB,OAAO,CAAC,OAAO,CAAC,CAkBlB;AAGD;;;;;;;;GAQG;AACH,wBAAsB,0BAA0B,CAC9C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,EAC7B,KAAK,EAAE,QAAQ,GACd,OAAO,CAAC,OAAO,CAAC,CAUlB;AAGD;;;;;;GAMG;AACH,wBAAsB,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,CASvF;AAGD;;;;;GAKG;AACH,wBAAsB,iBAAiB,CACrC,KAAK,EAAE,QAAQ,EACf,OAAO,GAAE,iCAAuE,GAC/E,OAAO,CAAC,SAAS,CAAC,CAgBpB;AAGD;;;;GAIG;AACH,wBAAsB,cAAc,CAAC,EAAE,kBAA0B,EAAE,GAAE,aAAkB,GAAG,OAAO,CAC/F,KAAK,EAAE,CACR,CAKA;AAGD;;;;;GAKG;AACH,wBAAsB,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAQjE;AAGD;;;;;;;;;;;;;;GAcG;AACH,wBAAsB,gBAAgB,CACpC,SAAS,EAAE,MAAM,EACjB,KAAK,CAAC,EAAE,QAAQ,EAChB,SAAS,GAAE,OAAc,EACzB,oBAAoB,CAAC,EAAE,MAAM,GAC5B,OAAO,CAAC,KAAK,CAAC,CA4BhB;AAGD;;;;;;;;;GASG;AACH,wBAAsB,iBAAiB,CACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,EAC7B,WAAW,GAAE,OAAe,GAC3B,OAAO,CAAC,OAAO,CAAC,CAYlB;AAGD;;;;GAIG;AACH,wBAAsB,cAAc,CAAC,aAAa,GAAE,aAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAuCjG;AAGD;;;;;;;;;;GAUG;AACH,wBAAgB,WAAW,CACzB,QAAQ,EAAE,CAAC,KAAK,EAAE,6BAA6B,KAAK,IAAI,GACvD,iBAAiB,CAEnB;AAGD,wBAAgB,kBAAkB,CAAC,YAAY,EAAE,iBAAiB,GAAG,IAAI,CAExE;AAGD;;GAEG;AACH,wBAAgB,kBAAkB,IAAI,IAAI,CAEzC;AAGD;;;;;GAKG;AACH,wBAAsB,eAAe,iBAMpC;AAGD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAsB,yBAAyB,CAAC,KAAK,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAM9E;AAGD;;;;;;GAMG;AACH,wBAAsB,wBAAwB,CAAC,KAAK,EAAE,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,CAMhF"}