name: Test Android e2e

on:
  workflow_call:
    inputs:
      test-tags:
        type: string
        required: false
        default: 'android'
  workflow_dispatch:
    inputs:
      test-tags:
        description: 'Maestro tags to run (comma separated)'
        required: false
        default: 'android'

env:
  APP_PATH: examples/Rn071/android/app/build/outputs/apk/release/app-release.apk
  EXAMPLE_SRC: ./examples/Rn071/src/**

jobs:
  test-android-e2e:
    name: Android - e2e
    runs-on: ubuntu-latest
    environment: e2e Android

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - uses: actions/cache@v4
        id: cache-android
        with:
          path: ${{ env.APP_PATH }}
          key: ${{ runner.os }}-${{ hashFiles('src/**', 'android/**', env.EXAMPLE_SRC, env.APP_PATH ) }}

      - uses: mobile-dev-inc/action-maestro-cloud@v1.8.0
        id: upload
        with:
          api-key: ${{ secrets.MAESTRO_CLOUD_API_KEY }}
          app-file: ${{ env.APP_PATH }}
          include-tags: ${{ inputs.test-tags }}

      # Step can be removed when this is fixed:
      # https://github.com/mobile-dev-inc/action-maestro-cloud/issues/28
      - name: Check for  maestro errors
        run: |
          if [[ "$MAESTRO_CLOUD_FLOW_RESULTS" == *"ERROR"* || "$MAESTRO_CLOUD_FLOW_RESULTS" == *"CANCELED"* ]]; then
            echo "One or more tests failed or were canceled."
            exit 1
          fi
        env:
          MAESTRO_CLOUD_FLOW_RESULTS: ${{ steps.upload.outputs.MAESTRO_CLOUD_FLOW_RESULTS }}
