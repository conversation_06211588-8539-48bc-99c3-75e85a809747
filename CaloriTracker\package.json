{"name": "caloritracker", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.17", "@react-navigation/native": "^7.1.13", "@react-navigation/stack": "^7.3.6", "axios": "^1.10.0", "expo": "~53.0.12", "expo-camera": "^16.1.8", "expo-image-picker": "^16.1.4", "expo-media-library": "^17.1.7", "expo-permissions": "^14.4.0", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^5.0.13", "react-native-gesture-handler": "^2.26.0", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}