# React Native Date Picker

A cross platform <a href="https://github.com/henninghall/react-native-date-picker" title="React Native Date Pickers">react native date picker</a> component for android and ios. It includes 3 different modes: date, time, and datetime. The date picker is customizable and has multiple language support.

## Modal

The first option is to use the built-in modal.

<table>
   <tr>
  <td><img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/react-native-datetime-picker-modal-ios.gif" alt="React Native DateTime Picker Modal iOS" height="400px" style="margin-left:10px" /></td>
        <td><img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/react-native-datetime-picker-modal-android.gif" alt="React Native DateTime Picker Modal Android" height="400px" style="margin-left:10px" />
    </td>
  </tr>
      <tr>
  <td align="center">iOS</td><td align="center">Android</td>
  </tr>
  </table>

## Inlined

The second option is to use the inlined picker. For instance in a view or a custom made modal.

<table>
   <tr>
  <td><img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/react-native-date-time-picker-ios-inline.gif" alt="React Native DateTime Picker" height="400px" style="margin-left:10px" /></td>
        <td><img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/react-native-date-time-picker-android-inline.gif" alt="React Native Date Time Picker" height="400px" style="margin-left:10px" />
    </td>
  </tr>
      <tr>
  <td align="center">iOS</td><td align="center">Android</td>
  </tr>
  </table>

## Installation

See <a href="https://github.com/henninghall/react-native-date-picker">github page</a> for installation instructions.

## Documentation

See <a href="https://github.com/henninghall/react-native-date-picker">github page</a> for documentation and more info.

## Examples

See <a href="https://github.com/henninghall/react-native-date-picker">github page</a> for code examples.

## Modes

### React Native Timepicker

<table><tr><td>
    <img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/time-mode-ios.png" alt="react native timepicker" height="120px" 
/>
</td><td>
    <img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/time-mode-android.png" alt="react native timepicker android" height="120px" />
</td></tr></table>

More info about the <a href="https://github.com/henninghall/react-native-date-picker#time-picker">react native timepicker</a>.

### React Native Datepicker

<table><tr><td>
    <img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/date-mode-ios.png" alt="react native datepicker" height="120px" 
/>
</td><td>
    <img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/date-mode-android.png" alt="react native datepicker android" height="120px" />
</td></tr></table>

More info about the <a href="https://github.com/henninghall/react-native-date-picker#datepicker">react native datepicker</a>.

### React Native Datetimepicker

<table>
<tr>
    <td align="center"><img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/react-native-date-picker.gif" alt="React Native Date Picker" title="React Native Date Picker" height="120px"/>
</td>
<td align="center">
 <img src="https://github.com/henninghall/react-native-date-picker/raw/master/docs/react-native-date-picker-android.gif" alt="react native datetimepicker" height="120px" />
</td><td align="center">
 <img src="https://raw.githubusercontent.com/henninghall/react-native-date-picker/master/docs/react-native-date-picker-android-native.gif" alt="react native datetimepicker android" height="120px" />
</td></tr>
</table>

More info about the <a href="https://github.com/henninghall/react-native-date-picker#date-time-picker">react native datetimepicker</a>.

<br>
<a href="https://github.com/henninghall/react-native-date-picker#date-time-picker"><h2>Visit github page → </h2></a>
