{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.medialibrary", "version": "17.1.7", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.3"}}], "files": [{"name": "expo.modules.medialibrary-17.1.7.aar", "url": "expo.modules.medialibrary-17.1.7.aar", "size": 205570, "sha512": "9b336313fe40409b98e15cd5b72c4abac3bb8fbac3f610a1a4dbc40be0a2cfb62e96c9c0631a367853980d471788e0846e25909c1326d997360e778cd7372f8b", "sha256": "b542fff8315e26ccd2f500bfbba02e146c4482b3404e4df3dd0f9c81295da5c9", "sha1": "7af3ca7465098e9841b386e32209d66425cf9ff6", "md5": "89b357a9263d25a4e766522aa6487a12"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.2.0"}}, {"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.3"}}], "files": [{"name": "expo.modules.medialibrary-17.1.7.aar", "url": "expo.modules.medialibrary-17.1.7.aar", "size": 205570, "sha512": "9b336313fe40409b98e15cd5b72c4abac3bb8fbac3f610a1a4dbc40be0a2cfb62e96c9c0631a367853980d471788e0846e25909c1326d997360e778cd7372f8b", "sha256": "b542fff8315e26ccd2f500bfbba02e146c4482b3404e4df3dd0f9c81295da5c9", "sha1": "7af3ca7465098e9841b386e32209d66425cf9ff6", "md5": "89b357a9263d25a4e766522aa6487a12"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.medialibrary-17.1.7-sources.jar", "url": "expo.modules.medialibrary-17.1.7-sources.jar", "size": 32541, "sha512": "79330a714bd7644779d62aa3aad74812230b1c25e9fd77d914144fcc471b9802453997e0a31e8244fa8d5917765be947f48c64b47801d4ca3316e1d5fd2c1498", "sha256": "9d0966a4c1c2ca74e27c5ad8ecf92336c7ed1ae8771494bd71f47f655744b2c4", "sha1": "efa987dd90118fdf0d09e5f146c45d2d90703d2e", "md5": "c282a7d9aeae94e08332f17e43542130"}]}]}