{"version": 3, "file": "PermissionsHooks.js", "sourceRoot": "", "sources": ["../src/PermissionsHooks.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAEjE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAGnD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,cAAc,CAC5B,IAAuC,EACvC,UAA8B,EAAE;IAEhC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/B,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAsB,CAAC;IACvD,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IAC5C,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAElD,0DAA0D;IAC1D,oFAAoF;IACpF,kFAAkF;IAElF,MAAM,cAAc,GAAG,WAAW,CAChC,GAAG,EAAE,CACH,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,OAAO,CAAC,QAAQ,CAAC,CAAC;SACnB;IACH,CAAC,CAAC,EACJ,CAAC,IAAI,CAAC,CACP,CAAC;IAEF,MAAM,cAAc,GAAG,WAAW,CAChC,GAAG,EAAE,CACH,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,OAAO,CAAC,QAAQ,CAAC,CAAC;SACnB;IACH,CAAC,CAAC,EACJ,CAAC,IAAI,CAAC,CACP,CAAC;IAEF,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,GAAG,EAAE;YACP,cAAc,EAAE,CAAC;SAClB;QAED,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE;YACf,cAAc,EAAE,CAAC;SAClB;IACH,CAAC,EAAE,CAAC,GAAG,EAAE,cAAc,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC,CAAC;IAE/C,SAAS,CAAC,GAAG,EAAE;QACb,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;AAChD,CAAC", "sourcesContent": ["import { useCallback, useRef, useEffect, useState } from 'react';\n\nimport { askAsync, getAsync } from './Permissions';\nimport { PermissionResponse, PermissionType } from './Permissions.types';\n\n/**\n * Get or ask permission for protected functionality within the app.\n * It returns the permission response after fetching or asking it.\n * The hook fetches the permissions when rendered, by default.\n * To ask the user permission, use the `askPermission` callback or `ask` option.\n *\n * @see https://docs.expo.dev/versions/latest/sdk/permissions/\n * @example\n * ```tsx\n * const [permission, askPermission, getPermission] = usePermissions(Permissions.CAMERA);\n *\n * return permission?.granted\n *   ? <Camera ... />\n *   : <Button onPress={askPermission} />;\n * ```\n */\nexport function usePermissions(\n  type: PermissionType | PermissionType[],\n  options: PermissionsOptions = {}\n): [PermissionResponse | undefined, () => Promise<void>, () => Promise<void>] {\n  const isMounted = useRef(true);\n  const [data, setData] = useState<PermissionResponse>();\n  const { ask = false, get = true } = options;\n  const types = Array.isArray(type) ? type : [type];\n\n  // note: its intentional to listen to `type`, not `types`.\n  // when `type` is casted to an array, it possible creates a new one on every render.\n  // to prevent unnecessary function instances we need to listen to the \"raw\" value.\n\n  const askPermissions = useCallback(\n    () =>\n      askAsync(...types).then((response) => {\n        if (isMounted.current) {\n          setData(response);\n        }\n      }),\n    [type]\n  );\n\n  const getPermissions = useCallback(\n    () =>\n      getAsync(...types).then((response) => {\n        if (isMounted.current) {\n          setData(response);\n        }\n      }),\n    [type]\n  );\n\n  useEffect(() => {\n    if (ask) {\n      askPermissions();\n    }\n\n    if (!ask && get) {\n      getPermissions();\n    }\n  }, [ask, askPermissions, get, getPermissions]);\n\n  useEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  return [data, askPermissions, getPermissions];\n}\n\nexport interface PermissionsOptions {\n  /** If it should ask the permissions when mounted, defaults to `false` */\n  ask?: boolean;\n  /** If it should fetch information about the permissions when mounted, defaults to `true` */\n  get?: boolean;\n}\n"]}