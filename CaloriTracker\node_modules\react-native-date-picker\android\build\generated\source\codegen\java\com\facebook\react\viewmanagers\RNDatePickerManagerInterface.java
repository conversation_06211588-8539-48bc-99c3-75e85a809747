/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;

public interface RNDatePickerManagerInterface<T extends View> {
  void setLocale(T view, @Nullable String value);
  void setDate(T view, @Nullable String value);
  void setMaximumDate(T view, @Nullable String value);
  void setMinimumDate(T view, @Nullable String value);
  void setMinuteInterval(T view, int value);
  void setMode(T view, @Nullable String value);
  void setTimeZoneOffsetInMinutes(T view, @Nullable String value);
  void setTextColor(T view, @Nullable String value);
  void setDividerColor(T view, @Nullable String value);
  void setButtonColor(T view, @Nullable String value);
  void setIs24hourSource(T view, @Nullable String value);
  void setTheme(T view, @Nullable String value);
  void setModal(T view, boolean value);
  void setOpen(T view, boolean value);
  void setConfirmText(T view, @Nullable String value);
  void setCancelText(T view, @Nullable String value);
  void setTitle(T view, @Nullable String value);
}
