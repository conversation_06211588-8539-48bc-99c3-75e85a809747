import StorageService from './storageService';
import GeminiService from './geminiService';
import CameraService from './cameraService';
import { Meal, FoodItem, NutritionInfo, UserProfile } from '../models';
import { formatNutritionData, calculateNutritionPercentages } from '../utils/apiUtils';

/**
 * Data Service - Main service for all data operations
 * Acts as a facade for storage, API, and other data-related services
 */
class DataService {
  constructor() {
    this.initialized = false;
  }

  /**
   * Initialize the data service
   */
  async initialize() {
    try {
      await StorageService.initialize();
      this.initialized = true;
      console.log('Data service initialized');
    } catch (error) {
      console.error('Error initializing data service:', error);
      throw error;
    }
  }

  // MEAL OPERATIONS

  /**
   * Create a new meal from image analysis
   * @param {string} imageUri - URI of the food image
   * @param {Object} mealInfo - Additional meal information
   * @returns {Promise<Object>} Result with meal data
   */
  async createMealFromImage(imageUri, mealInfo = {}) {
    try {
      // Validate image
      const imageValidation = CameraService.validateImage({ uri: imageUri });
      if (!imageValidation.isValid) {
        return {
          success: false,
          error: imageValidation.error,
        };
      }

      // Analyze image with Gemini API
      const analysisResult = await GeminiService.analyzeFoodImage(imageUri);
      
      if (!analysisResult.success) {
        return {
          success: false,
          error: analysisResult.error,
        };
      }

      // Create food items from analysis
      const foodItems = analysisResult.data.foodItems.map(item => 
        new FoodItem({
          name: item.name,
          portion: item.portion,
          nutrition: new NutritionInfo(item),
          confidence: analysisResult.data.confidence || 0.8,
        })
      );

      // Create meal
      const meal = new Meal({
        name: mealInfo.name || this.generateMealName(foodItems),
        type: mealInfo.type || 'other',
        timestamp: mealInfo.timestamp || new Date(),
        imageUri: imageUri,
        foodItems: foodItems,
        totalNutrition: new NutritionInfo(analysisResult.data.totalNutrition),
        analysisConfidence: analysisResult.data.confidence || 0.8,
        isAnalyzed: true,
        notes: analysisResult.data.notes || '',
        tags: mealInfo.tags || [],
      });

      // Save meal
      const saveSuccess = await StorageService.saveMeal(meal);
      
      if (!saveSuccess) {
        return {
          success: false,
          error: 'Failed to save meal',
        };
      }

      return {
        success: true,
        meal: meal,
        analysisData: analysisResult.data,
      };
    } catch (error) {
      console.error('Error creating meal from image:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Save a meal
   * @param {Meal} meal - Meal to save
   * @returns {Promise<boolean>} Success status
   */
  async saveMeal(meal) {
    return await StorageService.saveMeal(meal);
  }

  /**
   * Get all meals
   * @returns {Promise<Meal[]>} Array of meals
   */
  async getAllMeals() {
    return await StorageService.getAllMeals();
  }

  /**
   * Get meal by ID
   * @param {string} mealId - Meal ID
   * @returns {Promise<Meal|null>} Meal or null
   */
  async getMealById(mealId) {
    return await StorageService.getMealById(mealId);
  }

  /**
   * Get today's meals
   * @returns {Promise<Meal[]>} Today's meals
   */
  async getTodaysMeals() {
    return await StorageService.getTodaysMeals();
  }

  /**
   * Get meals for a specific date
   * @param {Date} date - Date to filter by
   * @returns {Promise<Meal[]>} Meals for the date
   */
  async getMealsByDate(date) {
    return await StorageService.getMealsByDate(date);
  }

  /**
   * Delete a meal
   * @param {string} mealId - Meal ID to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteMeal(mealId) {
    return await StorageService.deleteMeal(mealId);
  }

  /**
   * Update meal
   * @param {string} mealId - Meal ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<boolean>} Success status
   */
  async updateMeal(mealId, updates) {
    try {
      const meal = await StorageService.getMealById(mealId);
      if (!meal) {
        return false;
      }

      // Apply updates
      Object.assign(meal, updates);
      meal.isManuallyEdited = true;
      
      // Recalculate nutrition if food items were updated
      if (updates.foodItems) {
        meal.calculateTotalNutrition();
      }

      return await StorageService.saveMeal(meal);
    } catch (error) {
      console.error('Error updating meal:', error);
      return false;
    }
  }

  // NUTRITION ANALYSIS

  /**
   * Get daily nutrition summary
   * @param {Date} date - Date to get summary for (defaults to today)
   * @returns {Promise<Object>} Daily nutrition summary
   */
  async getDailyNutritionSummary(date = new Date()) {
    try {
      const meals = await StorageService.getMealsByDate(date);
      const goals = await StorageService.getDailyGoals();

      // Calculate total nutrition for the day
      const totalNutrition = meals.reduce(
        (total, meal) => total.add(meal.totalNutrition),
        new NutritionInfo()
      );

      // Calculate percentages of daily goals
      const percentages = calculateNutritionPercentages(totalNutrition.toObject(), goals);

      // Group meals by type
      const mealsByType = {
        breakfast: meals.filter(m => m.type === 'breakfast'),
        lunch: meals.filter(m => m.type === 'lunch'),
        dinner: meals.filter(m => m.type === 'dinner'),
        snack: meals.filter(m => m.type === 'snack'),
        other: meals.filter(m => m.type === 'other'),
      };

      return {
        date: date.toISOString().split('T')[0],
        totalNutrition: totalNutrition.toObject(),
        goals: goals,
        percentages: percentages,
        mealCount: meals.length,
        mealsByType: mealsByType,
        remainingCalories: Math.max(0, goals.calories - totalNutrition.calories),
        isGoalMet: {
          calories: totalNutrition.calories >= goals.calories * 0.9 && totalNutrition.calories <= goals.calories * 1.1,
          protein: totalNutrition.protein >= goals.protein * 0.8,
          carbs: totalNutrition.carbs >= goals.carbs * 0.8,
          fat: totalNutrition.fat >= goals.fat * 0.8,
        },
      };
    } catch (error) {
      console.error('Error getting daily nutrition summary:', error);
      return null;
    }
  }

  /**
   * Get weekly nutrition summary
   * @param {Date} startDate - Start of the week
   * @returns {Promise<Object>} Weekly nutrition summary
   */
  async getWeeklyNutritionSummary(startDate) {
    try {
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);

      const stats = await StorageService.getNutritionStats(startDate, endDate);
      const dailySummaries = [];

      // Get daily summaries for each day of the week
      for (let i = 0; i < 7; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        const dailySummary = await this.getDailyNutritionSummary(date);
        dailySummaries.push(dailySummary);
      }

      return {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        stats: stats,
        dailySummaries: dailySummaries,
      };
    } catch (error) {
      console.error('Error getting weekly nutrition summary:', error);
      return null;
    }
  }

  // USER PROFILE OPERATIONS

  /**
   * Get user profile
   * @returns {Promise<UserProfile>} User profile
   */
  async getUserProfile() {
    return await StorageService.getUserProfile();
  }

  /**
   * Save user profile
   * @param {UserProfile} profile - User profile to save
   * @returns {Promise<boolean>} Success status
   */
  async saveUserProfile(profile) {
    return await StorageService.saveUserProfile(profile);
  }

  /**
   * Update user profile
   * @param {Object} updates - Updates to apply
   * @returns {Promise<boolean>} Success status
   */
  async updateUserProfile(updates) {
    try {
      const profile = await StorageService.getUserProfile();
      profile.update(updates);
      return await StorageService.saveUserProfile(profile);
    } catch (error) {
      console.error('Error updating user profile:', error);
      return false;
    }
  }

  // GOALS AND SETTINGS

  /**
   * Get daily nutrition goals
   * @returns {Promise<Object>} Daily goals
   */
  async getDailyGoals() {
    return await StorageService.getDailyGoals();
  }

  /**
   * Save daily nutrition goals
   * @param {Object} goals - Daily goals
   * @returns {Promise<boolean>} Success status
   */
  async saveDailyGoals(goals) {
    return await StorageService.saveDailyGoals(goals);
  }

  /**
   * Get app settings
   * @returns {Promise<Object>} App settings
   */
  async getAppSettings() {
    return await StorageService.getAppSettings();
  }

  /**
   * Save app settings
   * @param {Object} settings - App settings
   * @returns {Promise<boolean>} Success status
   */
  async saveAppSettings(settings) {
    return await StorageService.saveAppSettings(settings);
  }

  // UTILITY METHODS

  /**
   * Generate meal name from food items
   * @param {FoodItem[]} foodItems - Array of food items
   * @returns {string} Generated meal name
   */
  generateMealName(foodItems) {
    if (foodItems.length === 0) {
      return 'Unknown Meal';
    }
    
    if (foodItems.length === 1) {
      return foodItems[0].name;
    }
    
    if (foodItems.length <= 3) {
      return foodItems.map(item => item.name).join(', ');
    }
    
    return `${foodItems[0].name} and ${foodItems.length - 1} other items`;
  }

  /**
   * Search meals by name or food items
   * @param {string} query - Search query
   * @returns {Promise<Meal[]>} Matching meals
   */
  async searchMeals(query) {
    try {
      const allMeals = await StorageService.getAllMeals();
      const lowercaseQuery = query.toLowerCase();
      
      return allMeals.filter(meal => {
        // Search in meal name
        if (meal.name.toLowerCase().includes(lowercaseQuery)) {
          return true;
        }
        
        // Search in food item names
        return meal.foodItems.some(item => 
          item.name.toLowerCase().includes(lowercaseQuery)
        );
      });
    } catch (error) {
      console.error('Error searching meals:', error);
      return [];
    }
  }

  /**
   * Get nutrition information for a specific food
   * @param {string} foodName - Name of the food
   * @param {string} portion - Portion size
   * @returns {Promise<Object>} Nutrition information
   */
  async getFoodNutrition(foodName, portion = '1 serving') {
    return await GeminiService.getFoodNutrition(foodName, portion);
  }

  /**
   * Export all data
   * @returns {Promise<Object>} Exported data
   */
  async exportData() {
    return await StorageService.exportData();
  }

  /**
   * Import data
   * @param {Object} data - Data to import
   * @returns {Promise<boolean>} Success status
   */
  async importData(data) {
    return await StorageService.importData(data);
  }

  /**
   * Clear all data
   * @returns {Promise<boolean>} Success status
   */
  async clearAllData() {
    return await StorageService.clearAll();
  }
}

export default new DataService();
