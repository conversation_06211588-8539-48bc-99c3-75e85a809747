import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  Button,
  NutritionDisplay,
  MacroDistributionChart,
  theme,
  commonStyles,
} from '../components';

import DataService from '../services/dataService';
import { formatDate, formatTime } from '../utils/apiUtils';

/**
 * Meal Detail Screen - Shows detailed information about a specific meal
 */
const MealDetailScreen = ({ route, navigation }) => {
  const { mealId } = route.params;

  const [meal, setMeal] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMeal();
  }, [mealId]);

  const loadMeal = async () => {
    try {
      setLoading(true);
      const mealData = await DataService.getMealById(mealId);

      if (mealData) {
        setMeal(mealData);
      } else {
        Alert.alert('Error', 'Meal not found.');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error loading meal:', error);
      Alert.alert('Error', 'Failed to load meal details.');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleEditMeal = () => {
    navigation.navigate('EditMeal', { mealId: meal.id });
  };

  const handleDeleteMeal = () => {
    Alert.alert(
      'Delete Meal',
      'Are you sure you want to delete this meal? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await DataService.deleteMeal(meal.id);
              if (success) {
                Alert.alert('Success', 'Meal deleted successfully.');
                navigation.goBack();
              } else {
                Alert.alert('Error', 'Failed to delete meal.');
              }
            } catch (error) {
              console.error('Error deleting meal:', error);
              Alert.alert('Error', 'Failed to delete meal.');
            }
          },
        },
      ]
    );
  };

  const renderMealHeader = () => {
    return (
      <View style={styles.headerContainer}>
        <View style={styles.mealInfo}>
          <Text style={styles.mealName}>{meal.name}</Text>
          <Text style={styles.mealTime}>
            {formatDate(meal.timestamp)} • {formatTime(meal.timestamp)}
          </Text>
          <Text style={styles.mealType}>
            {meal.type.charAt(0).toUpperCase() + meal.type.slice(1)}
          </Text>
        </View>

        {meal.imageUri && (
          <Image source={{ uri: meal.imageUri }} style={styles.mealImage} />
        )}
      </View>
    );
  };

  const renderFoodItems = () => {
    if (meal.foodItems.length === 0) return null;

    return (
      <View style={styles.foodItemsContainer}>
        <Text style={styles.sectionTitle}>Food Items</Text>

        {meal.foodItems.map((item, index) => (
          <View key={item.id || index} style={styles.foodItem}>
            <View style={styles.foodItemHeader}>
              <Text style={styles.foodItemName}>{item.name}</Text>
              <Text style={styles.foodItemPortion}>{item.portion}</Text>
            </View>

            <View style={styles.foodItemNutrition}>
              <Text style={styles.nutritionText}>
                {item.nutrition.calories} cal • {item.nutrition.protein}g protein •
                {item.nutrition.carbs}g carbs • {item.nutrition.fat}g fat
              </Text>
            </View>

            {item.confidence < 0.8 && (
              <Text style={styles.confidenceWarning}>
                ⚠️ Low confidence ({Math.round(item.confidence * 100)}%)
              </Text>
            )}
          </View>
        ))}
      </View>
    );
  };

  const renderNutritionSummary = () => {
    return (
      <View style={styles.nutritionContainer}>
        <Text style={styles.sectionTitle}>Nutrition Summary</Text>

        <NutritionDisplay
          nutrition={meal.totalNutrition.toObject()}
          showPercentages={false}
          showBars={false}
          compact={false}
        />

        <MacroDistributionChart
          nutrition={meal.totalNutrition.toObject()}
        />
      </View>
    );
  };

  const renderMealNotes = () => {
    if (!meal.notes) return null;

    return (
      <View style={styles.notesContainer}>
        <Text style={styles.sectionTitle}>Notes</Text>
        <Text style={styles.notesText}>{meal.notes}</Text>
      </View>
    );
  };

  const renderMealTags = () => {
    if (!meal.tags || meal.tags.length === 0) return null;

    return (
      <View style={styles.tagsContainer}>
        <Text style={styles.sectionTitle}>Tags</Text>
        <View style={styles.tagsRow}>
          {meal.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderActions = () => {
    return (
      <View style={styles.actionsContainer}>
        <Button
          title="Edit Meal"
          variant="outline"
          onPress={handleEditMeal}
          style={styles.actionButton}
        />
        <Button
          title="Delete Meal"
          variant="danger"
          onPress={handleDeleteMeal}
          style={styles.actionButton}
        />
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Loading meal details...</Text>
      </SafeAreaView>
    );
  }

  if (!meal) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Meal not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        {renderMealHeader()}
        {renderFoodItems()}
        {renderNutritionSummary()}
        {renderMealNotes()}
        {renderMealTags()}
        {renderActions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  contentContainer: {
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
  },

  headerContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },

  mealInfo: {
    flex: 1,
    marginRight: theme.spacing.md,
  },

  mealName: {
    ...theme.typography.h4,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.xs,
  },

  mealTime: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    marginBottom: theme.spacing.xs,
  },

  mealType: {
    ...theme.typography.body2,
    color: theme.colors.primary,
    fontWeight: '600',
    textTransform: 'capitalize',
  },

  mealImage: {
    width: 80,
    height: 80,
    borderRadius: theme.borderRadius.md,
  },

  sectionTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.md,
  },

  foodItemsContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  foodItem: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
    paddingVertical: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },

  foodItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },

  foodItemName: {
    ...theme.typography.body1,
    color: theme.colors.onSurface,
    fontWeight: '600',
    flex: 1,
  },

  foodItemPortion: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
  },

  foodItemNutrition: {
    marginBottom: theme.spacing.xs,
  },

  nutritionText: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
  },

  confidenceWarning: {
    ...theme.typography.caption,
    color: theme.colors.warning,
    fontStyle: 'italic',
  },

  nutritionContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  notesContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  notesText: {
    ...theme.typography.body2,
    color: theme.colors.gray700,
    lineHeight: 20,
  },

  tagsContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  tagsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },

  tag: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.round,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
  },

  tagText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontWeight: '600',
  },

  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.md,
  },

  actionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
});

export default MealDetailScreen;
