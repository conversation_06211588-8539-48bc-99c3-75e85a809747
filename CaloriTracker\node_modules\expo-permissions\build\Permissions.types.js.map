{"version": 3, "file": "Permissions.types.js", "sourceRoot": "", "sources": ["../src/Permissions.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,gBAAgB,GAEjB,MAAM,mBAAmB,CAAC;AAoC3B,OAAO,EAAE,gBAAgB,EAAE,CAAC", "sourcesContent": ["import {\n  PermissionResponse as EXPermissionResponse,\n  PermissionStatus,\n  PermissionExpiration,\n} from 'expo-modules-core';\n\nexport type PermissionType =\n  | 'camera'\n  | 'cameraRoll'\n  | 'mediaLibrary'\n  | 'mediaLibraryWriteOnly'\n  | 'audioRecording'\n  | 'location'\n  | 'locationForeground'\n  | 'locationBackground'\n  | 'userFacingNotifications'\n  | 'notifications'\n  | 'contacts'\n  | 'calendar'\n  | 'reminders'\n  | 'motion'\n  | 'systemBrightness';\n\nexport interface PermissionResponse extends EXPermissionResponse {\n  permissions: PermissionMap;\n}\n\nexport interface PermissionMap {\n  [permissionType: string /* PermissionType */]: PermissionInfo;\n}\n\nexport interface PermissionInfo extends EXPermissionResponse {\n  /**\n   * iOS only - Permission.MEDIA_LIBRARY/MEDIA_LIBRARY_WRITE_ONLY\n   */\n  accessPrivileges?: 'all' | 'limited' | 'none';\n  scope?: 'whenInUse' | 'always' | 'none';\n  android?: PermissionDetailsLocationAndroid;\n}\n\nexport { PermissionStatus };\n\nexport { PermissionExpiration };\n\nexport type PermissionDetailsLocationAndroid = {\n  accuracy: 'fine' | 'coarse' | 'none';\n};\n"]}