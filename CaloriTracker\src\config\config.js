/**
 * Application Configuration
 */

export const API_CONFIG = {
  GEMINI_API_KEY: 'AIzaSyAs0vPoI2hA-oBKiLYYC2qVnKSZSzAR5XE',
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
  REQUEST_TIMEOUT: 30000, // 30 seconds
};

export const APP_CONFIG = {
  APP_NAME: 'CaloriTracker',
  VERSION: '1.0.0',
  DEFAULT_DAILY_CALORIE_GOAL: 2000,
  DEFAULT_PROTEIN_GOAL: 150, // grams
  DEFAULT_CARB_GOAL: 250, // grams
  DEFAULT_FAT_GOAL: 65, // grams
};

export const STORAGE_KEYS = {
  MEALS: '@CaloriTracker:meals',
  USER_PROFILE: '@CaloriTracker:userProfile',
  DAILY_GOALS: '@CaloriTracker:dailyGoals',
  APP_SETTINGS: '@CaloriTracker:appSettings',
};

export const NUTRITION_GOALS = {
  CALORIES: {
    min: 1200,
    max: 4000,
    default: 2000,
  },
  PROTEIN: {
    min: 50,
    max: 300,
    default: 150,
  },
  CARBS: {
    min: 100,
    max: 500,
    default: 250,
  },
  FAT: {
    min: 30,
    max: 200,
    default: 65,
  },
};

export const COLORS = {
  primary: '#6200EE',
  primaryVariant: '#3700B3',
  secondary: '#03DAC6',
  secondaryVariant: '#018786',
  background: '#FFFFFF',
  surface: '#FFFFFF',
  error: '#B00020',
  onPrimary: '#FFFFFF',
  onSecondary: '#000000',
  onBackground: '#000000',
  onSurface: '#000000',
  onError: '#FFFFFF',
  
  // Custom colors for nutrition tracking
  calories: '#FF6B6B',
  protein: '#4ECDC4',
  carbs: '#45B7D1',
  fat: '#FFA07A',
  
  // Chart colors
  chartColors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F'],
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  info: '#2196F3',
};

export const CHART_CONFIG = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(98, 0, 238, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#6200EE',
  },
};

export default {
  API_CONFIG,
  APP_CONFIG,
  STORAGE_KEYS,
  NUTRITION_GOALS,
  COLORS,
  CHART_CONFIG,
};
