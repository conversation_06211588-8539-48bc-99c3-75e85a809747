import * as Camera from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Linking, Platform } from 'react-native';

/**
 * Permissions utility for managing app permissions
 */

export const PERMISSION_TYPES = {
  CAMERA: 'camera',
  MEDIA_LIBRARY: 'mediaLibrary',
  PHOTO_LIBRARY: 'photoLibrary',
};

export const PERMISSION_STATUS = {
  GRANTED: 'granted',
  DENIED: 'denied',
  UNDETERMINED: 'undetermined',
};

/**
 * Check camera permission status
 * @returns {Promise<string>} Permission status
 */
export const checkCameraPermission = async () => {
  try {
    const { status } = await Camera.getCameraPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error checking camera permission:', error);
    return PERMISSION_STATUS.DENIED;
  }
};

/**
 * Request camera permission
 * @returns {Promise<string>} Permission status
 */
export const requestCameraPermission = async () => {
  try {
    const { status } = await Camera.requestCameraPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error requesting camera permission:', error);
    return PERMISSION_STATUS.DENIED;
  }
};

/**
 * Check media library permission status
 * @returns {Promise<string>} Permission status
 */
export const checkMediaLibraryPermission = async () => {
  try {
    const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error checking media library permission:', error);
    return PERMISSION_STATUS.DENIED;
  }
};

/**
 * Request media library permission
 * @returns {Promise<string>} Permission status
 */
export const requestMediaLibraryPermission = async () => {
  try {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error requesting media library permission:', error);
    return PERMISSION_STATUS.DENIED;
  }
};

/**
 * Check if permission is granted
 * @param {string} permissionType - Type of permission to check
 * @returns {Promise<boolean>} Whether permission is granted
 */
export const isPermissionGranted = async (permissionType) => {
  let status;
  
  switch (permissionType) {
    case PERMISSION_TYPES.CAMERA:
      status = await checkCameraPermission();
      break;
    case PERMISSION_TYPES.MEDIA_LIBRARY:
    case PERMISSION_TYPES.PHOTO_LIBRARY:
      status = await checkMediaLibraryPermission();
      break;
    default:
      return false;
  }
  
  return status === PERMISSION_STATUS.GRANTED;
};

/**
 * Request permission with user-friendly messaging
 * @param {string} permissionType - Type of permission to request
 * @returns {Promise<boolean>} Whether permission was granted
 */
export const requestPermission = async (permissionType) => {
  let status;
  
  switch (permissionType) {
    case PERMISSION_TYPES.CAMERA:
      status = await requestCameraPermission();
      break;
    case PERMISSION_TYPES.MEDIA_LIBRARY:
    case PERMISSION_TYPES.PHOTO_LIBRARY:
      status = await requestMediaLibraryPermission();
      break;
    default:
      return false;
  }
  
  return status === PERMISSION_STATUS.GRANTED;
};

/**
 * Show permission denied alert with option to open settings
 * @param {string} permissionType - Type of permission that was denied
 */
export const showPermissionDeniedAlert = (permissionType) => {
  let title, message;
  
  switch (permissionType) {
    case PERMISSION_TYPES.CAMERA:
      title = 'Camera Permission Required';
      message = 'CaloriTracker needs camera access to take photos of your meals. Please enable camera permission in your device settings.';
      break;
    case PERMISSION_TYPES.MEDIA_LIBRARY:
    case PERMISSION_TYPES.PHOTO_LIBRARY:
      title = 'Photo Library Permission Required';
      message = 'CaloriTracker needs photo library access to select images from your gallery. Please enable photo library permission in your device settings.';
      break;
    default:
      title = 'Permission Required';
      message = 'CaloriTracker needs additional permissions to function properly. Please enable the required permissions in your device settings.';
  }
  
  Alert.alert(
    title,
    message,
    [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Open Settings',
        onPress: () => {
          if (Platform.OS === 'ios') {
            Linking.openURL('app-settings:');
          } else {
            Linking.openSettings();
          }
        },
      },
    ]
  );
};

/**
 * Ensure permission is granted, request if needed
 * @param {string} permissionType - Type of permission to ensure
 * @returns {Promise<boolean>} Whether permission is granted
 */
export const ensurePermission = async (permissionType) => {
  // First check if permission is already granted
  const isGranted = await isPermissionGranted(permissionType);
  
  if (isGranted) {
    return true;
  }
  
  // Request permission if not granted
  const granted = await requestPermission(permissionType);
  
  if (!granted) {
    showPermissionDeniedAlert(permissionType);
    return false;
  }
  
  return true;
};

/**
 * Check all required permissions for the app
 * @returns {Promise<Object>} Status of all permissions
 */
export const checkAllPermissions = async () => {
  const permissions = {
    camera: await isPermissionGranted(PERMISSION_TYPES.CAMERA),
    mediaLibrary: await isPermissionGranted(PERMISSION_TYPES.MEDIA_LIBRARY),
  };
  
  return {
    ...permissions,
    allGranted: Object.values(permissions).every(granted => granted),
  };
};

/**
 * Request all required permissions for the app
 * @returns {Promise<Object>} Status of all permissions after requesting
 */
export const requestAllPermissions = async () => {
  const results = {
    camera: await ensurePermission(PERMISSION_TYPES.CAMERA),
    mediaLibrary: await ensurePermission(PERMISSION_TYPES.MEDIA_LIBRARY),
  };
  
  return {
    ...results,
    allGranted: Object.values(results).every(granted => granted),
  };
};

/**
 * Show initial permissions explanation
 */
export const showPermissionsExplanation = () => {
  Alert.alert(
    'Permissions Required',
    'CaloriTracker needs access to your camera and photo library to help you track your meals by analyzing food images.\n\n• Camera: Take photos of your meals\n• Photo Library: Select existing food photos',
    [
      {
        text: 'Not Now',
        style: 'cancel',
      },
      {
        text: 'Grant Permissions',
        onPress: requestAllPermissions,
      },
    ]
  );
};

/**
 * Handle permission result and show appropriate feedback
 * @param {string} permissionType - Type of permission
 * @param {boolean} granted - Whether permission was granted
 */
export const handlePermissionResult = (permissionType, granted) => {
  if (!granted) {
    showPermissionDeniedAlert(permissionType);
  }
};

export default {
  PERMISSION_TYPES,
  PERMISSION_STATUS,
  checkCameraPermission,
  requestCameraPermission,
  checkMediaLibraryPermission,
  requestMediaLibraryPermission,
  isPermissionGranted,
  requestPermission,
  showPermissionDeniedAlert,
  ensurePermission,
  checkAllPermissions,
  requestAllPermissions,
  showPermissionsExplanation,
  handlePermissionResult,
};
