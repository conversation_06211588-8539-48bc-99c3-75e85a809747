import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { theme, commonStyles } from '../../theme/theme';

/**
 * Custom Input Component with label, error handling, and various types
 */
const Input = ({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  type = 'text', // 'text', 'number', 'email', 'password'
  multiline = false,
  numberOfLines = 1,
  maxLength,
  editable = true,
  required = false,
  leftIcon,
  rightIcon,
  onRightIconPress,
  style,
  inputStyle,
  containerStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const getKeyboardType = () => {
    switch (type) {
      case 'number':
        return 'numeric';
      case 'email':
        return 'email-address';
      default:
        return 'default';
    }
  };

  const getSecureTextEntry = () => {
    return type === 'password' && !showPassword;
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const renderPasswordToggle = () => {
    if (type !== 'password') return null;

    return (
      <TouchableOpacity
        onPress={togglePasswordVisibility}
        style={styles.passwordToggle}
      >
        <Text style={styles.passwordToggleText}>
          {showPassword ? '🙈' : '👁️'}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[commonStyles.label, styles.label]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={[
        commonStyles.input,
        styles.inputContainer,
        isFocused && commonStyles.inputFocused,
        error && styles.inputError,
        !editable && styles.inputDisabled,
        style,
      ]}>
        {leftIcon && (
          <View style={styles.leftIcon}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.gray500}
          keyboardType={getKeyboardType()}
          secureTextEntry={getSecureTextEntry()}
          multiline={multiline}
          numberOfLines={numberOfLines}
          maxLength={maxLength}
          editable={editable}
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={[
            styles.input,
            multiline && styles.multilineInput,
            inputStyle,
          ]}
          {...props}
        />
        
        {type === 'password' && renderPasswordToggle()}
        
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            style={styles.rightIcon}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={commonStyles.errorText}>
          {error}
        </Text>
      )}
      
      {maxLength && value && (
        <Text style={styles.characterCount}>
          {value.length}/{maxLength}
        </Text>
      )}
    </View>
  );
};

/**
 * Specialized input components
 */

// Number Input with increment/decrement buttons
export const NumberInput = ({
  value,
  onChangeText,
  min = 0,
  max = 9999,
  step = 1,
  unit,
  ...props
}) => {
  const numericValue = parseFloat(value) || 0;

  const increment = () => {
    const newValue = Math.min(numericValue + step, max);
    onChangeText(newValue.toString());
  };

  const decrement = () => {
    const newValue = Math.max(numericValue - step, min);
    onChangeText(newValue.toString());
  };

  const handleTextChange = (text) => {
    const numValue = parseFloat(text) || 0;
    if (numValue >= min && numValue <= max) {
      onChangeText(text);
    }
  };

  return (
    <View style={styles.numberInputContainer}>
      <Input
        {...props}
        value={value}
        onChangeText={handleTextChange}
        type="number"
        style={styles.numberInput}
      />
      
      <View style={styles.numberControls}>
        <TouchableOpacity
          onPress={increment}
          style={styles.numberButton}
          disabled={numericValue >= max}
        >
          <Text style={styles.numberButtonText}>+</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={decrement}
          style={styles.numberButton}
          disabled={numericValue <= min}
        >
          <Text style={styles.numberButtonText}>-</Text>
        </TouchableOpacity>
      </View>
      
      {unit && (
        <Text style={styles.unit}>{unit}</Text>
      )}
    </View>
  );
};

// Search Input with clear button
export const SearchInput = ({
  value,
  onChangeText,
  onClear,
  placeholder = 'Search...',
  ...props
}) => {
  const handleClear = () => {
    onChangeText('');
    onClear && onClear();
  };

  return (
    <Input
      {...props}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      leftIcon={<Text style={styles.searchIcon}>🔍</Text>}
      rightIcon={value ? <Text style={styles.clearIcon}>✕</Text> : null}
      onRightIconPress={value ? handleClear : undefined}
      style={styles.searchInput}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  
  label: {
    marginBottom: theme.spacing.xs,
  },
  
  required: {
    color: theme.colors.error,
  },
  
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  
  input: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.onSurface,
    paddingVertical: 0,
  },
  
  multilineInput: {
    textAlignVertical: 'top',
    paddingVertical: theme.spacing.sm,
  },
  
  inputError: {
    borderColor: theme.colors.error,
    borderWidth: 2,
  },
  
  inputDisabled: {
    backgroundColor: theme.colors.gray100,
    color: theme.colors.gray500,
  },
  
  leftIcon: {
    marginRight: theme.spacing.sm,
  },
  
  rightIcon: {
    marginLeft: theme.spacing.sm,
    padding: theme.spacing.xs,
  },
  
  passwordToggle: {
    marginLeft: theme.spacing.sm,
    padding: theme.spacing.xs,
  },
  
  passwordToggleText: {
    fontSize: 16,
  },
  
  characterCount: {
    ...theme.typography.caption,
    color: theme.colors.gray500,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },
  
  // Number Input Styles
  numberInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  
  numberInput: {
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  
  numberControls: {
    flexDirection: 'column',
  },
  
  numberButton: {
    backgroundColor: theme.colors.gray200,
    borderRadius: theme.borderRadius.sm,
    width: 32,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 1,
  },
  
  numberButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  
  unit: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    marginLeft: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  
  // Search Input Styles
  searchInput: {
    marginBottom: 0,
  },
  
  searchIcon: {
    fontSize: 16,
    color: theme.colors.gray500,
  },
  
  clearIcon: {
    fontSize: 14,
    color: theme.colors.gray500,
  },
});

export default Input;
