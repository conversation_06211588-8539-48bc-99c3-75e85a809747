{"name": "expo-permissions", "version": "14.4.0", "description": "Allows you prompt for various permissions to access device sensors, personal data, etc.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "permissions"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-permissions"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/permissions/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {}, "devDependencies": {"@testing-library/react-hooks": "^7.0.1", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "79607a7325f47aa17c36d266100d09a4ff2cc544"}