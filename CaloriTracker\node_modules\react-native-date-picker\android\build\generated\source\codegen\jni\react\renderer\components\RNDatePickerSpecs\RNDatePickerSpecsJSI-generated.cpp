/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNDatePickerSpecsJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeRNDatePickerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNDatePickerCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNDatePickerCxxSpecJSI_closePicker(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNDatePickerCxxSpecJSI *>(&turboModule)->closePicker(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNDatePickerCxxSpecJSI_openPicker(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNDatePickerCxxSpecJSI *>(&turboModule)->openPicker(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNDatePickerCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNDatePickerCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNDatePickerCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNDatePickerCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}

NativeRNDatePickerCxxSpecJSI::NativeRNDatePickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNDatePicker", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeRNDatePickerCxxSpecJSI_getConstants};
  methodMap_["closePicker"] = MethodMetadata {0, __hostFunction_NativeRNDatePickerCxxSpecJSI_closePicker};
  methodMap_["openPicker"] = MethodMetadata {1, __hostFunction_NativeRNDatePickerCxxSpecJSI_openPicker};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeRNDatePickerCxxSpecJSI_removeListeners};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeRNDatePickerCxxSpecJSI_addListener};
}


} // namespace facebook::react
