name: Test Android Unit

on:
  workflow_call:
  workflow_dispatch:

jobs:
  test-android-unit:
    name: Android - Unit
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: 'temurin'

      - name: Install npm dependencies
        working-directory: ./examples/Rn072
        run: yarn install --frozen-lockfile

      - name: Run unit tests
        working-directory: ./examples/Rn072/android
        run: ./gradlew testDebugUnitTest
