<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:layoutDirection="ltr"
>

    <LinearLayout
        android:id="@+id/pickerWrapper"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        >

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/year"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native"
            android:tag="year"
            android:contentDescription="@string/year_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/month"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native"
            android:tag="month"
            android:contentDescription="@string/month_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/date"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native_small"
            android:tag="date"
            android:contentDescription="@string/date_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/day"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native"
            android:tag="day"
            android:contentDescription="@string/day_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/hour"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native_small"
            android:tag="hour"
            android:contentDescription="@string/hour_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/minutes"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native_small"
            android:tag="minutes"
            android:contentDescription="@string/minutes_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <com.henninghall.date_picker.pickers.AndroidNative
            android:id="@+id/ampm"
            android:theme="@style/DatePickerTheme"
            style="@style/android_native"
            android:tag="ampm"
            android:contentDescription="@string/ampm_description"
            android:focusable="true"
            android:focusableInTouchMode="true" />
    </LinearLayout>

</LinearLayout>
