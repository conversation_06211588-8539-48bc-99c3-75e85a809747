name: PR

on:
  workflow_dispatch:
  pull_request_target:

jobs:
  build-ios:
    name: Build
    uses: ./.github/workflows/build-ios.yml

  build-android:
    name: Build
    uses: ./.github/workflows/build-android.yml

  test-ios-e2e:
    name: Test
    uses: ./.github/workflows/test-ios-e2e.yml
    needs: [build-ios]
    secrets: inherit

  test-android-e2e:
    name: Test
    uses: ./.github/workflows/test-android-e2e.yml
    needs: [build-android]
    secrets: inherit

  test-js:
    name: Test
    uses: ./.github/workflows/test-js.yml

  test-android-unit:
    name: Test
    uses: ./.github/workflows/test-android-unit.yml

  lint:
    name: Check
    uses: ./.github/workflows/lint.yml

  typecheck:
    name: Check
    uses: ./.github/workflows/typecheck.yml
