import axios from 'axios';

const GEMINI_API_KEY = 'AIzaSyAs0vPoI2hA-oBKiLYYC2qVnKSZSzAR5XE';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

/**
 * Gemini API Service for food image analysis
 */
class GeminiService {
  constructor() {
    this.apiKey = GEMINI_API_KEY;
    this.baseUrl = GEMINI_API_URL;
  }

  /**
   * Convert image to base64 format
   * @param {string} imageUri - Local image URI
   * @returns {Promise<string>} Base64 encoded image
   */
  async imageToBase64(imageUri) {
    try {
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = reader.result.split(',')[1]; // Remove data:image/jpeg;base64, prefix
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error converting image to base64:', error);
      throw error;
    }
  }

  /**
   * Analyze food image using Gemini API
   * @param {string} imageUri - Local image URI
   * @returns {Promise<Object>} Nutritional analysis result
   */
  async analyzeFoodImage(imageUri) {
    try {
      const base64Image = await this.imageToBase64(imageUri);
      
      const prompt = `
        Analyze this food image and provide detailed nutritional information. 
        Please identify all food items visible in the image and provide:
        
        1. List of identified food items with estimated portions
        2. Total calories for the entire meal
        3. Macronutrients breakdown (proteins, carbohydrates, fats) in grams
        4. Key micronutrients (vitamins, minerals) if significant
        5. Estimated serving size
        
        Format the response as a JSON object with the following structure:
        {
          "foodItems": [
            {
              "name": "food name",
              "portion": "estimated portion size",
              "calories": number,
              "protein": number,
              "carbs": number,
              "fat": number
            }
          ],
          "totalNutrition": {
            "calories": number,
            "protein": number,
            "carbs": number,
            "fat": number,
            "fiber": number,
            "sugar": number,
            "sodium": number
          },
          "confidence": number (0-1),
          "notes": "any additional observations or uncertainties"
        }
        
        If you cannot identify the food clearly, indicate low confidence and provide your best estimate.
      `;

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: "image/jpeg",
                  data: base64Image
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.1,
          topK: 32,
          topP: 1,
          maxOutputTokens: 2048,
        }
      };

      const response = await axios.post(
        `${this.baseUrl}?key=${this.apiKey}`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data && response.data.candidates && response.data.candidates[0]) {
        const content = response.data.candidates[0].content.parts[0].text;
        
        // Try to parse JSON from the response
        try {
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const nutritionData = JSON.parse(jsonMatch[0]);
            return {
              success: true,
              data: nutritionData,
              rawResponse: content
            };
          } else {
            // If no JSON found, create a structured response from text
            return {
              success: true,
              data: this.parseTextResponse(content),
              rawResponse: content
            };
          }
        } catch (parseError) {
          console.error('Error parsing JSON response:', parseError);
          return {
            success: true,
            data: this.parseTextResponse(content),
            rawResponse: content
          };
        }
      } else {
        throw new Error('Invalid response from Gemini API');
      }
    } catch (error) {
      console.error('Error analyzing food image:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Parse text response when JSON parsing fails
   * @param {string} textResponse - Raw text response from Gemini
   * @returns {Object} Structured nutrition data
   */
  parseTextResponse(textResponse) {
    // Basic parsing logic for text responses
    const defaultResponse = {
      foodItems: [
        {
          name: "Unidentified Food",
          portion: "1 serving",
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0
        }
      ],
      totalNutrition: {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0
      },
      confidence: 0.1,
      notes: "Could not parse detailed nutritional information. Please review and edit manually."
    };

    // Try to extract basic calorie information from text
    const calorieMatch = textResponse.match(/(\d+)\s*calories?/i);
    if (calorieMatch) {
      const calories = parseInt(calorieMatch[1]);
      defaultResponse.totalNutrition.calories = calories;
      defaultResponse.foodItems[0].calories = calories;
    }

    return defaultResponse;
  }

  /**
   * Get nutrition information for a specific food item by name
   * @param {string} foodName - Name of the food item
   * @param {string} portion - Portion size (optional)
   * @returns {Promise<Object>} Nutrition information
   */
  async getFoodNutrition(foodName, portion = "1 serving") {
    try {
      const prompt = `
        Provide detailed nutritional information for ${foodName} (${portion}).
        
        Format the response as a JSON object:
        {
          "name": "${foodName}",
          "portion": "${portion}",
          "calories": number,
          "protein": number,
          "carbs": number,
          "fat": number,
          "fiber": number,
          "sugar": number,
          "sodium": number,
          "vitamins": ["list of significant vitamins"],
          "minerals": ["list of significant minerals"]
        }
      `;

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.1,
          topK: 32,
          topP: 1,
          maxOutputTokens: 1024,
        }
      };

      const response = await axios.post(
        `${this.baseUrl}?key=${this.apiKey}`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data && response.data.candidates && response.data.candidates[0]) {
        const content = response.data.candidates[0].content.parts[0].text;
        
        try {
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const nutritionData = JSON.parse(jsonMatch[0]);
            return {
              success: true,
              data: nutritionData
            };
          }
        } catch (parseError) {
          console.error('Error parsing nutrition response:', parseError);
        }
      }

      return {
        success: false,
        error: 'Could not get nutrition information',
        data: null
      };
    } catch (error) {
      console.error('Error getting food nutrition:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }
}

export default new GeminiService();
