import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  Button,
  NutritionDisplay,
  MacroDistributionChart,
  Input,
  NumberInput,
  theme,
  commonStyles,
} from '../components';

import DataService from '../services/dataService';
import { FoodItem, NutritionInfo } from '../models';

/**
 * Analysis Screen - Shows AI analysis results and allows editing
 */
const AnalysisScreen = ({ route, navigation }) => {
  const { meal, analysisData, isNewMeal } = route.params;

  const [currentMeal, setCurrentMeal] = useState(meal);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editingFoodIndex, setEditingFoodIndex] = useState(-1);

  useEffect(() => {
    // Set initial meal data
    setCurrentMeal(meal);
  }, [meal]);

  const handleSaveMeal = async () => {
    try {
      setSaving(true);

      const success = await DataService.saveMeal(currentMeal);

      if (success) {
        Alert.alert(
          'Success',
          'Meal saved successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to home screen
                navigation.navigate('MainTabs', { screen: 'Home' });
              },
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to save meal. Please try again.');
      }
    } catch (error) {
      console.error('Error saving meal:', error);
      Alert.alert('Error', 'Failed to save meal. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleEditMeal = () => {
    setEditing(true);
  };

  const handleCancelEdit = () => {
    setEditing(false);
    setEditingFoodIndex(-1);
    // Reset to original meal data
    setCurrentMeal(meal);
  };

  const handleUpdateFoodItem = (index, updates) => {
    const updatedMeal = { ...currentMeal };
    updatedMeal.foodItems[index] = { ...updatedMeal.foodItems[index], ...updates };
    updatedMeal.calculateTotalNutrition();
    updatedMeal.isManuallyEdited = true;
    setCurrentMeal(updatedMeal);
  };

  const handleRemoveFoodItem = (index) => {
    Alert.alert(
      'Remove Food Item',
      'Are you sure you want to remove this food item?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            const updatedMeal = { ...currentMeal };
            updatedMeal.foodItems.splice(index, 1);
            updatedMeal.calculateTotalNutrition();
            updatedMeal.isManuallyEdited = true;
            setCurrentMeal(updatedMeal);
          },
        },
      ]
    );
  };

  const renderConfidenceBadge = () => {
    if (!analysisData?.confidence) return null;

    const confidence = Math.round(analysisData.confidence * 100);
    const badgeColor = confidence >= 80 ? theme.colors.success :
                      confidence >= 60 ? theme.colors.warning : theme.colors.error;

    return (
      <View style={[styles.confidenceBadge, { backgroundColor: badgeColor }]}>
        <Text style={styles.confidenceBadgeText}>
          {confidence}% AI Confidence
        </Text>
      </View>
    );
  };

  const renderMealImage = () => {
    if (!currentMeal.imageUri) return null;

    return (
      <View style={styles.imageContainer}>
        <Image source={{ uri: currentMeal.imageUri }} style={styles.mealImage} />
        {renderConfidenceBadge()}
      </View>
    );
  };

  const renderAnalysisNotes = () => {
    if (!analysisData?.notes) return null;

    return (
      <View style={styles.notesContainer}>
        <Text style={styles.notesTitle}>AI Analysis Notes</Text>
        <Text style={styles.notesText}>{analysisData.notes}</Text>
      </View>
    );
  };

  const renderFoodItemEditor = (item, index) => {
    if (editingFoodIndex !== index) {
      return (
        <View key={item.id} style={styles.foodItem}>
          <View style={styles.foodItemHeader}>
            <Text style={styles.foodItemName}>{item.name}</Text>
            <Text style={styles.foodItemPortion}>{item.portion}</Text>
          </View>

          <View style={styles.foodItemNutrition}>
            <Text style={styles.nutritionText}>
              {item.nutrition.calories} cal • {item.nutrition.protein}g protein •
              {item.nutrition.carbs}g carbs • {item.nutrition.fat}g fat
            </Text>
          </View>

          {editing && (
            <View style={styles.foodItemActions}>
              <TouchableOpacity
                onPress={() => setEditingFoodIndex(index)}
                style={styles.editButton}
              >
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleRemoveFoodItem(index)}
                style={styles.removeButton}
              >
                <Text style={styles.removeButtonText}>Remove</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      );
    }

    // Editing mode for this food item
    return (
      <View key={item.id} style={styles.foodItemEditor}>
        <Input
          label="Food Name"
          value={item.name}
          onChangeText={(value) => handleUpdateFoodItem(index, { name: value })}
        />

        <Input
          label="Portion Size"
          value={item.portion}
          onChangeText={(value) => handleUpdateFoodItem(index, { portion: value })}
        />

        <NumberInput
          label="Calories"
          value={item.nutrition.calories.toString()}
          onChangeText={(value) => {
            const nutrition = new NutritionInfo({
              ...item.nutrition.toObject(),
              calories: parseInt(value) || 0,
            });
            handleUpdateFoodItem(index, { nutrition });
          }}
          min={0}
          max={9999}
        />

        <View style={styles.editorActions}>
          <Button
            title="Done"
            onPress={() => setEditingFoodIndex(-1)}
            style={styles.doneButton}
          />
        </View>
      </View>
    );
  };

  const renderFoodItems = () => {
    return (
      <View style={styles.foodItemsContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Identified Food Items</Text>
        </View>

        {currentMeal.foodItems.map((item, index) =>
          renderFoodItemEditor(item, index)
        )}
      </View>
    );
  };

  const renderNutritionSummary = () => {
    return (
      <View style={styles.nutritionContainer}>
        <Text style={styles.sectionTitle}>Nutrition Summary</Text>

        <NutritionDisplay
          nutrition={currentMeal.totalNutrition.toObject()}
          showPercentages={false}
          showBars={false}
          compact={true}
        />

        <MacroDistributionChart
          nutrition={currentMeal.totalNutrition.toObject()}
        />
      </View>
    );
  };

  const renderActions = () => {
    if (editing) {
      return (
        <View style={styles.actionsContainer}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={handleCancelEdit}
            style={styles.actionButton}
          />
          <Button
            title="Save Changes"
            onPress={handleSaveMeal}
            loading={saving}
            disabled={saving}
            style={styles.actionButton}
          />
        </View>
      );
    }

    return (
      <View style={styles.actionsContainer}>
        <Button
          title="Edit Meal"
          variant="outline"
          onPress={handleEditMeal}
          style={styles.actionButton}
        />
        <Button
          title="Save Meal"
          onPress={handleSaveMeal}
          loading={saving}
          disabled={saving}
          style={styles.actionButton}
        />
      </View>
    );
  };

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        {renderMealImage()}
        {renderAnalysisNotes()}
        {renderFoodItems()}
        {renderNutritionSummary()}
        {renderActions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  contentContainer: {
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
  },

  imageContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  mealImage: {
    width: '100%',
    height: 200,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.sm,
  },

  confidenceBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },

  confidenceBadgeText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontWeight: '600',
  },

  notesContainer: {
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },

  notesTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.sm,
  },

  notesText: {
    ...theme.typography.body2,
    color: theme.colors.gray700,
    lineHeight: 20,
  },

  foodItemsContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },

  sectionTitle: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
  },

  editToggle: {
    ...theme.typography.body2,
    color: theme.colors.primary,
    fontWeight: '600',
  },

  foodItem: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
    paddingVertical: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },

  foodItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },

  foodItemName: {
    ...theme.typography.body1,
    color: theme.colors.onSurface,
    fontWeight: '600',
    flex: 1,
  },

  foodItemPortion: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
  },

  foodItemNutrition: {
    marginBottom: theme.spacing.xs,
  },

  nutritionText: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
  },

  foodItemActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: theme.spacing.sm,
  },

  editButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },

  editButtonText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontWeight: '600',
  },

  removeButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },

  removeButtonText: {
    ...theme.typography.caption,
    color: theme.colors.onError,
    fontWeight: '600',
  },

  foodItemEditor: {
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },

  editorActions: {
    alignItems: 'flex-end',
    marginTop: theme.spacing.sm,
  },

  doneButton: {
    minWidth: 80,
  },

  nutritionContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },

  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.md,
  },

  actionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
});

export default AnalysisScreen;
