import React from 'react';
import { View, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { commonStyles } from '../components';

const AnalysisScreen = ({ route }) => {
  const { meal, analysisData, isNewMeal } = route.params;

  return (
    <SafeAreaView style={commonStyles.centerContent}>
      <Text>Analysis Screen</Text>
      <Text>Meal: {meal?.name}</Text>
      <Text>Is New: {isNewMeal ? 'Yes' : 'No'}</Text>
    </SafeAreaView>
  );
};

export default AnalysisScreen;
