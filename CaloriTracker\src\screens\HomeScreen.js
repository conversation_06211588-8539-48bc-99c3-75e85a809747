import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  MealCard,
  NutritionDisplay,
  DailyProgressChart,
  Button,
  FAB,
  theme,
  commonStyles,
} from '../components';

import DataService from '../services/dataService';
import { formatDate } from '../utils/apiUtils';

/**
 * Home Screen - Shows today's meals and nutrition summary
 */
const HomeScreen = ({ navigation }) => {
  const [todaysMeals, setTodaysMeals] = useState([]);
  const [nutritionSummary, setNutritionSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadTodaysData = async () => {
    try {
      setLoading(true);
      
      // Load today's meals
      const meals = await DataService.getTodaysMeals();
      setTodaysMeals(meals);
      
      // Load nutrition summary
      const summary = await DataService.getDailyNutritionSummary();
      setNutritionSummary(summary);
      
    } catch (error) {
      console.error('Error loading today\'s data:', error);
      Alert.alert('Error', 'Failed to load today\'s data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTodaysData();
    setRefreshing(false);
  };

  // Load data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadTodaysData();
    }, [])
  );

  const handleMealPress = (meal) => {
    navigation.navigate('MealDetail', { mealId: meal.id });
  };

  const handleAddMeal = () => {
    navigation.navigate('Camera');
  };

  const handleGoalsPress = () => {
    navigation.navigate('Goals');
  };

  const renderWelcomeMessage = () => {
    const currentHour = new Date().getHours();
    let greeting = 'Good day';
    
    if (currentHour < 12) {
      greeting = 'Good morning';
    } else if (currentHour < 18) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }

    return (
      <View style={styles.welcomeContainer}>
        <Text style={styles.welcomeText}>{greeting}!</Text>
        <Text style={styles.dateText}>{formatDate(new Date())}</Text>
      </View>
    );
  };

  const renderNutritionSummary = () => {
    if (!nutritionSummary) return null;

    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Today's Progress</Text>
          <TouchableOpacity onPress={handleGoalsPress}>
            <Text style={styles.goalsLink}>Edit Goals</Text>
          </TouchableOpacity>
        </View>
        
        <NutritionDisplay
          nutrition={nutritionSummary.totalNutrition}
          goals={nutritionSummary.goals}
          showPercentages={true}
          showBars={true}
          compact={false}
        />
        
        <DailyProgressChart
          nutrition={nutritionSummary.totalNutrition}
          goals={nutritionSummary.goals}
        />
      </View>
    );
  };

  const renderQuickStats = () => {
    if (!nutritionSummary) return null;

    const { totalNutrition, goals, remainingCalories } = nutritionSummary;

    return (
      <View style={styles.quickStatsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{totalNutrition.calories}</Text>
          <Text style={styles.statLabel}>Consumed</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{remainingCalories}</Text>
          <Text style={styles.statLabel}>Remaining</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{todaysMeals.length}</Text>
          <Text style={styles.statLabel}>Meals</Text>
        </View>
      </View>
    );
  };

  const renderMealsList = () => {
    if (todaysMeals.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>🍽️</Text>
          <Text style={styles.emptyStateTitle}>No meals logged today</Text>
          <Text style={styles.emptyStateText}>
            Start tracking your nutrition by adding your first meal!
          </Text>
          <Button
            title="Add Your First Meal"
            onPress={handleAddMeal}
            style={styles.emptyStateButton}
          />
        </View>
      );
    }

    // Group meals by type
    const mealsByType = {
      breakfast: todaysMeals.filter(m => m.type === 'breakfast'),
      lunch: todaysMeals.filter(m => m.type === 'lunch'),
      dinner: todaysMeals.filter(m => m.type === 'dinner'),
      snack: todaysMeals.filter(m => m.type === 'snack'),
      other: todaysMeals.filter(m => m.type === 'other'),
    };

    return (
      <View style={styles.mealsContainer}>
        <Text style={styles.mealsTitle}>Today's Meals</Text>
        
        {Object.entries(mealsByType).map(([type, meals]) => {
          if (meals.length === 0) return null;
          
          return (
            <View key={type} style={styles.mealTypeSection}>
              <Text style={styles.mealTypeTitle}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
              {meals.map(meal => (
                <MealCard
                  key={meal.id}
                  meal={meal}
                  onPress={() => handleMealPress(meal)}
                  showImage={true}
                  showNutrition={true}
                  compact={false}
                />
              ))}
            </View>
          );
        })}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Loading today's data...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {renderWelcomeMessage()}
        {renderQuickStats()}
        {renderNutritionSummary()}
        {renderMealsList()}
      </ScrollView>
      
      <FAB
        onPress={handleAddMeal}
        icon={<Text style={styles.fabIcon}>+</Text>}
        style={commonStyles.fab}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  contentContainer: {
    paddingBottom: 100, // Space for FAB
  },
  
  welcomeContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.primary,
    marginBottom: theme.spacing.md,
  },
  
  welcomeText: {
    ...theme.typography.h3,
    color: theme.colors.onPrimary,
    marginBottom: theme.spacing.xs,
  },
  
  dateText: {
    ...theme.typography.body1,
    color: theme.colors.onPrimary,
    opacity: 0.8,
  },
  
  quickStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: theme.colors.surface,
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    paddingVertical: theme.spacing.md,
    ...theme.shadows.md,
  },
  
  statItem: {
    alignItems: 'center',
  },
  
  statValue: {
    ...theme.typography.h4,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  
  statLabel: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
    marginTop: theme.spacing.xs,
  },
  
  summaryContainer: {
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.md,
  },
  
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  
  summaryTitle: {
    ...theme.typography.h5,
    color: theme.colors.onSurface,
  },
  
  goalsLink: {
    ...theme.typography.body2,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  
  mealsContainer: {
    marginHorizontal: theme.spacing.md,
  },
  
  mealsTitle: {
    ...theme.typography.h5,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.md,
  },
  
  mealTypeSection: {
    marginBottom: theme.spacing.lg,
  },
  
  mealTypeTitle: {
    ...theme.typography.h6,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.sm,
    marginLeft: theme.spacing.sm,
  },
  
  emptyState: {
    ...commonStyles.emptyState,
    marginHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xxl,
  },
  
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.md,
  },
  
  emptyStateTitle: {
    ...theme.typography.h5,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  
  emptyStateText: {
    ...commonStyles.emptyStateText,
    marginBottom: theme.spacing.lg,
  },
  
  emptyStateButton: {
    alignSelf: 'center',
  },
  
  fabIcon: {
    fontSize: 24,
    color: theme.colors.onPrimary,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
