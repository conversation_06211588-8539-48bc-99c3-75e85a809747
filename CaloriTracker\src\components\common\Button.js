import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { theme, commonStyles } from '../../theme/theme';

/**
 * Custom Button Component with various styles and states
 */
const Button = ({
  title,
  onPress,
  variant = 'primary', // 'primary', 'secondary', 'outline', 'text', 'danger'
  size = 'medium', // 'small', 'medium', 'large'
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left', // 'left', 'right'
  fullWidth = false,
  style,
  textStyle,
  children,
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    // Size styles
    switch (size) {
      case 'small':
        baseStyle.push(styles.smallButton);
        break;
      case 'large':
        baseStyle.push(styles.largeButton);
        break;
      default:
        baseStyle.push(styles.mediumButton);
    }
    
    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.secondaryButton);
        break;
      case 'outline':
        baseStyle.push(styles.outlineButton);
        break;
      case 'text':
        baseStyle.push(styles.textButton);
        break;
      case 'danger':
        baseStyle.push(styles.dangerButton);
        break;
      default:
        baseStyle.push(styles.primaryButton);
    }
    
    // State styles
    if (disabled) {
      baseStyle.push(styles.disabledButton);
    }
    
    if (fullWidth) {
      baseStyle.push(styles.fullWidthButton);
    }
    
    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    // Size styles
    switch (size) {
      case 'small':
        baseStyle.push(styles.smallButtonText);
        break;
      case 'large':
        baseStyle.push(styles.largeButtonText);
        break;
      default:
        baseStyle.push(styles.mediumButtonText);
    }
    
    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.secondaryButtonText);
        break;
      case 'outline':
        baseStyle.push(styles.outlineButtonText);
        break;
      case 'text':
        baseStyle.push(styles.textButtonText);
        break;
      case 'danger':
        baseStyle.push(styles.dangerButtonText);
        break;
      default:
        baseStyle.push(styles.primaryButtonText);
    }
    
    if (disabled) {
      baseStyle.push(styles.disabledButtonText);
    }
    
    return baseStyle;
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={variant === 'primary' || variant === 'danger' ? theme.colors.onPrimary : theme.colors.primary}
          />
          {title && (
            <Text style={[getTextStyle(), styles.loadingText, textStyle]}>
              {title}
            </Text>
          )}
        </View>
      );
    }

    if (children) {
      return children;
    }

    return (
      <View style={styles.contentContainer}>
        {icon && iconPosition === 'left' && (
          <View style={styles.leftIcon}>
            {icon}
          </View>
        )}
        
        {title && (
          <Text style={[getTextStyle(), textStyle]}>
            {title}
          </Text>
        )}
        
        {icon && iconPosition === 'right' && (
          <View style={styles.rightIcon}>
            {icon}
          </View>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

/**
 * Specialized button components
 */

// Floating Action Button
export const FAB = ({
  onPress,
  icon,
  size = 56,
  backgroundColor = theme.colors.primary,
  style,
  ...props
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.fab,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor,
        },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      {...props}
    >
      {icon}
    </TouchableOpacity>
  );
};

// Icon Button
export const IconButton = ({
  onPress,
  icon,
  size = 'medium',
  variant = 'text',
  style,
  ...props
}) => {
  const iconSize = size === 'small' ? 32 : size === 'large' ? 56 : 44;
  
  return (
    <TouchableOpacity
      style={[
        styles.iconButton,
        {
          width: iconSize,
          height: iconSize,
          borderRadius: iconSize / 2,
        },
        variant === 'outline' && styles.iconButtonOutline,
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
      {...props}
    >
      {icon}
    </TouchableOpacity>
  );
};

// Button Group
export const ButtonGroup = ({
  buttons,
  selectedIndex,
  onPress,
  style,
}) => {
  return (
    <View style={[styles.buttonGroup, style]}>
      {buttons.map((button, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.groupButton,
            index === 0 && styles.firstGroupButton,
            index === buttons.length - 1 && styles.lastGroupButton,
            selectedIndex === index && styles.selectedGroupButton,
          ]}
          onPress={() => onPress(index)}
          activeOpacity={0.7}
        >
          <Text
            style={[
              styles.groupButtonText,
              selectedIndex === index && styles.selectedGroupButtonText,
            ]}
          >
            {button}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  
  // Size styles
  smallButton: {
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    minHeight: 36,
  },
  
  mediumButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    minHeight: 48,
  },
  
  largeButton: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    minHeight: 56,
  },
  
  // Variant styles
  primaryButton: {
    backgroundColor: theme.colors.primary,
    ...theme.shadows.sm,
  },
  
  secondaryButton: {
    backgroundColor: theme.colors.secondary,
    ...theme.shadows.sm,
  },
  
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  
  textButton: {
    backgroundColor: 'transparent',
  },
  
  dangerButton: {
    backgroundColor: theme.colors.error,
    ...theme.shadows.sm,
  },
  
  disabledButton: {
    backgroundColor: theme.colors.gray300,
    elevation: 0,
    shadowOpacity: 0,
  },
  
  fullWidthButton: {
    width: '100%',
  },
  
  // Text styles
  buttonText: {
    fontWeight: '600',
    textAlign: 'center',
  },
  
  smallButtonText: {
    fontSize: 14,
  },
  
  mediumButtonText: {
    fontSize: 16,
  },
  
  largeButtonText: {
    fontSize: 18,
  },
  
  primaryButtonText: {
    color: theme.colors.onPrimary,
  },
  
  secondaryButtonText: {
    color: theme.colors.onSecondary,
  },
  
  outlineButtonText: {
    color: theme.colors.primary,
  },
  
  textButtonText: {
    color: theme.colors.primary,
  },
  
  dangerButtonText: {
    color: theme.colors.onError,
  },
  
  disabledButtonText: {
    color: theme.colors.gray500,
  },
  
  // Content styles
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  loadingText: {
    marginLeft: theme.spacing.sm,
  },
  
  leftIcon: {
    marginRight: theme.spacing.sm,
  },
  
  rightIcon: {
    marginLeft: theme.spacing.sm,
  },
  
  // FAB styles
  fab: {
    alignItems: 'center',
    justifyContent: 'center',
    ...theme.shadows.lg,
  },
  
  // Icon Button styles
  iconButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  iconButtonOutline: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
  },
  
  // Button Group styles
  buttonGroup: {
    flexDirection: 'row',
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    overflow: 'hidden',
  },
  
  groupButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    borderRightWidth: 1,
    borderRightColor: theme.colors.primary,
  },
  
  firstGroupButton: {
    borderTopLeftRadius: theme.borderRadius.md,
    borderBottomLeftRadius: theme.borderRadius.md,
  },
  
  lastGroupButton: {
    borderTopRightRadius: theme.borderRadius.md,
    borderBottomRightRadius: theme.borderRadius.md,
    borderRightWidth: 0,
  },
  
  selectedGroupButton: {
    backgroundColor: theme.colors.primary,
  },
  
  groupButtonText: {
    ...theme.typography.button,
    color: theme.colors.primary,
  },
  
  selectedGroupButtonText: {
    color: theme.colors.onPrimary,
  },
});

export default Button;
