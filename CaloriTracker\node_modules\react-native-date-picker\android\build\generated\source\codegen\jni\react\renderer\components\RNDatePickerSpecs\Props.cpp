
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include <react/renderer/components/RNDatePickerSpecs/Props.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

RNDatePickerProps::RNDatePickerProps(
    const PropsParserContext &context,
    const RNDatePickerProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    locale(convertRawProp(context, rawProps, "locale", sourceProps.locale, {})),
    date(convertRawProp(context, rawProps, "date", sourceProps.date, {})),
    maximumDate(convertRawProp(context, rawProps, "maximumDate", sourceProps.maximumDate, {})),
    minimumDate(convertRawProp(context, rawProps, "minimumDate", sourceProps.minimumDate, {})),
    minuteInterval(convertRawProp(context, rawProps, "minuteInterval", sourceProps.minuteInterval, {0})),
    mode(convertRawProp(context, rawProps, "mode", sourceProps.mode, {RNDatePickerMode::Datetime})),
    timeZoneOffsetInMinutes(convertRawProp(context, rawProps, "timeZoneOffsetInMinutes", sourceProps.timeZoneOffsetInMinutes, {})),
    textColor(convertRawProp(context, rawProps, "textColor", sourceProps.textColor, {})),
    dividerColor(convertRawProp(context, rawProps, "dividerColor", sourceProps.dividerColor, {})),
    buttonColor(convertRawProp(context, rawProps, "buttonColor", sourceProps.buttonColor, {})),
    is24hourSource(convertRawProp(context, rawProps, "is24hourSource", sourceProps.is24hourSource, {RNDatePickerIs24hourSource::Device})),
    theme(convertRawProp(context, rawProps, "theme", sourceProps.theme, {RNDatePickerTheme::Auto})),
    modal(convertRawProp(context, rawProps, "modal", sourceProps.modal, {false})),
    open(convertRawProp(context, rawProps, "open", sourceProps.open, {false})),
    confirmText(convertRawProp(context, rawProps, "confirmText", sourceProps.confirmText, {})),
    cancelText(convertRawProp(context, rawProps, "cancelText", sourceProps.cancelText, {})),
    title(convertRawProp(context, rawProps, "title", sourceProps.title, {}))
      {}

} // namespace facebook::react
