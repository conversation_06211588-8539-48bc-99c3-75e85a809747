import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  StyleSheet,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  MealCard,
  SearchInput,
  ButtonGroup,
  WeeklyCaloriesChart,
  theme,
  commonStyles,
} from '../components';

import DataService from '../services/dataService';
import { formatDate, getDateRange } from '../utils/apiUtils';

/**
 * History Screen - Shows meal history with filtering and search
 */
const HistoryScreen = ({ navigation }) => {
  const [meals, setMeals] = useState([]);
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState(0); // 0: All, 1: Today, 2: Week, 3: Month
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [weeklyData, setWeeklyData] = useState(null);

  const periods = ['All', 'Today', 'Week', 'Month'];

  const loadMeals = async () => {
    try {
      setLoading(true);
      const allMeals = await DataService.getAllMeals();
      
      // Sort meals by timestamp (newest first)
      const sortedMeals = allMeals.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
      );
      
      setMeals(sortedMeals);
      filterMeals(sortedMeals, searchQuery, selectedPeriod);
      
      // Load weekly data for chart
      await loadWeeklyData();
      
    } catch (error) {
      console.error('Error loading meals:', error);
      Alert.alert('Error', 'Failed to load meal history. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadWeeklyData = async () => {
    try {
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Start from Sunday
      
      const weeklyStats = await DataService.getWeeklyNutritionSummary(startOfWeek);
      
      if (weeklyStats && weeklyStats.dailySummaries) {
        const weeklyCalories = weeklyStats.dailySummaries.map(
          day => day?.totalNutrition?.calories || 0
        );
        setWeeklyData(weeklyCalories);
      }
    } catch (error) {
      console.error('Error loading weekly data:', error);
    }
  };

  const filterMeals = (mealList, query, period) => {
    let filtered = [...mealList];

    // Filter by search query
    if (query.trim()) {
      const lowercaseQuery = query.toLowerCase();
      filtered = filtered.filter(meal => 
        meal.name.toLowerCase().includes(lowercaseQuery) ||
        meal.foodItems.some(item => 
          item.name.toLowerCase().includes(lowercaseQuery)
        ) ||
        meal.notes.toLowerCase().includes(lowercaseQuery)
      );
    }

    // Filter by time period
    if (period > 0) {
      const now = new Date();
      let dateRange;
      
      switch (period) {
        case 1: // Today
          dateRange = getDateRange('today');
          break;
        case 2: // Week
          dateRange = getDateRange('week');
          break;
        case 3: // Month
          dateRange = getDateRange('month');
          break;
        default:
          dateRange = null;
      }
      
      if (dateRange) {
        filtered = filtered.filter(meal => {
          const mealDate = new Date(meal.timestamp);
          return mealDate >= dateRange.start && mealDate <= dateRange.end;
        });
      }
    }

    setFilteredMeals(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMeals();
    setRefreshing(false);
  };

  // Load data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadMeals();
    }, [])
  );

  // Update filtered meals when search or period changes
  useEffect(() => {
    filterMeals(meals, searchQuery, selectedPeriod);
  }, [searchQuery, selectedPeriod, meals]);

  const handleMealPress = (meal) => {
    navigation.navigate('MealDetail', { mealId: meal.id });
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handlePeriodChange = (index) => {
    setSelectedPeriod(index);
  };

  const renderMealItem = ({ item }) => (
    <MealCard
      meal={item}
      onPress={() => handleMealPress(item)}
      showImage={true}
      showNutrition={true}
      compact={false}
    />
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Weekly Chart */}
      {weeklyData && (
        <View style={styles.chartContainer}>
          <WeeklyCaloriesChart 
            weeklyData={weeklyData}
            goal={2000} // TODO: Get from user goals
          />
        </View>
      )}
      
      {/* Search */}
      <SearchInput
        value={searchQuery}
        onChangeText={handleSearchChange}
        placeholder="Search meals..."
        style={styles.searchInput}
      />
      
      {/* Period Filter */}
      <ButtonGroup
        buttons={periods}
        selectedIndex={selectedPeriod}
        onPress={handlePeriodChange}
        style={styles.periodFilter}
      />
      
      {/* Results Count */}
      <Text style={styles.resultsCount}>
        {filteredMeals.length} meal{filteredMeals.length !== 1 ? 's' : ''} found
      </Text>
    </View>
  );

  const renderEmptyState = () => {
    if (loading) return null;
    
    const isFiltered = searchQuery.trim() || selectedPeriod > 0;
    
    return (
      <View style={styles.emptyState}>
        <Text style={styles.emptyStateIcon}>
          {isFiltered ? '🔍' : '📱'}
        </Text>
        <Text style={styles.emptyStateTitle}>
          {isFiltered ? 'No meals found' : 'No meals logged yet'}
        </Text>
        <Text style={styles.emptyStateText}>
          {isFiltered 
            ? 'Try adjusting your search or filter criteria'
            : 'Start tracking your nutrition by adding your first meal!'
          }
        </Text>
      </View>
    );
  };

  const renderSectionHeader = (date) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionHeaderText}>
        {formatDate(new Date(date))}
      </Text>
    </View>
  );

  // Group meals by date for better organization
  const groupedMeals = filteredMeals.reduce((groups, meal) => {
    const date = meal.getDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(meal);
    return groups;
  }, {});

  const renderGroupedMeals = () => {
    const dates = Object.keys(groupedMeals).sort((a, b) => new Date(b) - new Date(a));
    
    return dates.map(date => (
      <View key={date}>
        {renderSectionHeader(date)}
        {groupedMeals[date].map(meal => (
          <MealCard
            key={meal.id}
            meal={meal}
            onPress={() => handleMealPress(meal)}
            showImage={true}
            showNutrition={true}
            compact={false}
          />
        ))}
      </View>
    ));
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Loading meal history...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <FlatList
        style={styles.container}
        data={[]} // We'll use renderGroupedMeals instead
        renderItem={null}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={styles.contentContainer}
      />
      
      {filteredMeals.length > 0 && (
        <View style={styles.mealsContainer}>
          {renderGroupedMeals()}
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  contentContainer: {
    flexGrow: 1,
  },
  
  headerContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background,
  },
  
  chartContainer: {
    marginBottom: theme.spacing.lg,
  },
  
  searchInput: {
    marginBottom: theme.spacing.md,
  },
  
  periodFilter: {
    marginBottom: theme.spacing.md,
  },
  
  resultsCount: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  
  mealsContainer: {
    paddingBottom: theme.spacing.lg,
  },
  
  sectionHeader: {
    backgroundColor: theme.colors.gray100,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    marginTop: theme.spacing.md,
  },
  
  sectionHeaderText: {
    ...theme.typography.h6,
    color: theme.colors.onBackground,
    fontWeight: 'bold',
  },
  
  emptyState: {
    ...commonStyles.emptyState,
    paddingVertical: theme.spacing.xxl,
  },
  
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.md,
  },
  
  emptyStateTitle: {
    ...theme.typography.h5,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  
  emptyStateText: {
    ...commonStyles.emptyStateText,
  },
});

export default HistoryScreen;
