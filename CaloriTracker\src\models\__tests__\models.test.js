/**
 * Tests for Data Models
 */

import { NutritionInfo, FoodItem, Meal, UserProfile } from '../index';

describe('NutritionInfo', () => {
  it('should create nutrition info with default values', () => {
    const nutrition = new NutritionInfo();
    
    expect(nutrition.calories).toBe(0);
    expect(nutrition.protein).toBe(0);
    expect(nutrition.carbs).toBe(0);
    expect(nutrition.fat).toBe(0);
  });

  it('should create nutrition info with provided values', () => {
    const nutrition = new NutritionInfo({
      calories: 250,
      protein: 15.5,
      carbs: 30.2,
      fat: 8.7,
    });
    
    expect(nutrition.calories).toBe(250);
    expect(nutrition.protein).toBe(15.5);
    expect(nutrition.carbs).toBe(30.2);
    expect(nutrition.fat).toBe(8.7);
  });

  it('should round values appropriately', () => {
    const nutrition = new NutritionInfo({
      calories: 250.7,
      protein: 15.567,
      carbs: 30.234,
      fat: 8.789,
    });
    
    expect(nutrition.calories).toBe(251);
    expect(nutrition.protein).toBe(15.6);
    expect(nutrition.carbs).toBe(30.2);
    expect(nutrition.fat).toBe(8.8);
  });

  it('should add nutrition values correctly', () => {
    const nutrition1 = new NutritionInfo({ calories: 100, protein: 10, carbs: 15, fat: 5 });
    const nutrition2 = new NutritionInfo({ calories: 150, protein: 8, carbs: 20, fat: 7 });
    
    const combined = nutrition1.add(nutrition2);
    
    expect(combined.calories).toBe(250);
    expect(combined.protein).toBe(18);
    expect(combined.carbs).toBe(35);
    expect(combined.fat).toBe(12);
  });

  it('should scale nutrition values correctly', () => {
    const nutrition = new NutritionInfo({ calories: 100, protein: 10, carbs: 15, fat: 5 });
    
    const scaled = nutrition.scale(1.5);
    
    expect(scaled.calories).toBe(150);
    expect(scaled.protein).toBe(15);
    expect(scaled.carbs).toBe(22.5);
    expect(scaled.fat).toBe(7.5);
  });

  it('should convert to and from object', () => {
    const originalData = {
      calories: 250,
      protein: 15.5,
      carbs: 30.2,
      fat: 8.7,
      fiber: 5.2,
    };
    
    const nutrition = new NutritionInfo(originalData);
    const obj = nutrition.toObject();
    const restored = NutritionInfo.fromObject(obj);
    
    expect(restored.calories).toBe(nutrition.calories);
    expect(restored.protein).toBe(nutrition.protein);
    expect(restored.carbs).toBe(nutrition.carbs);
    expect(restored.fat).toBe(nutrition.fat);
    expect(restored.fiber).toBe(nutrition.fiber);
  });
});

describe('FoodItem', () => {
  it('should create food item with default values', () => {
    const foodItem = new FoodItem();
    
    expect(foodItem.id).toBeDefined();
    expect(foodItem.name).toBe('');
    expect(foodItem.portion).toBe('1 serving');
    expect(foodItem.nutrition).toBeInstanceOf(NutritionInfo);
    expect(foodItem.confidence).toBe(1.0);
    expect(foodItem.isManuallyEdited).toBe(false);
  });

  it('should create food item with provided values', () => {
    const nutrition = new NutritionInfo({ calories: 95, protein: 0.5, carbs: 25, fat: 0.3 });
    const foodItem = new FoodItem({
      name: 'Apple',
      portion: '1 medium',
      nutrition: nutrition,
      confidence: 0.9,
    });
    
    expect(foodItem.name).toBe('Apple');
    expect(foodItem.portion).toBe('1 medium');
    expect(foodItem.nutrition.calories).toBe(95);
    expect(foodItem.confidence).toBe(0.9);
  });

  it('should update nutrition and mark as manually edited', () => {
    const foodItem = new FoodItem({ name: 'Apple' });
    
    foodItem.updateNutrition({ calories: 100, protein: 1, carbs: 26, fat: 0.5 });
    
    expect(foodItem.nutrition.calories).toBe(100);
    expect(foodItem.isManuallyEdited).toBe(true);
  });

  it('should convert to and from object', () => {
    const nutrition = new NutritionInfo({ calories: 95, protein: 0.5, carbs: 25, fat: 0.3 });
    const foodItem = new FoodItem({
      name: 'Apple',
      portion: '1 medium',
      nutrition: nutrition,
      confidence: 0.9,
    });
    
    const obj = foodItem.toObject();
    const restored = FoodItem.fromObject(obj);
    
    expect(restored.name).toBe(foodItem.name);
    expect(restored.portion).toBe(foodItem.portion);
    expect(restored.nutrition.calories).toBe(foodItem.nutrition.calories);
    expect(restored.confidence).toBe(foodItem.confidence);
  });
});

describe('Meal', () => {
  it('should create meal with default values', () => {
    const meal = new Meal();
    
    expect(meal.id).toBeDefined();
    expect(meal.name).toBe('');
    expect(meal.type).toBe('other');
    expect(meal.timestamp).toBeInstanceOf(Date);
    expect(meal.foodItems).toEqual([]);
    expect(meal.totalNutrition).toBeInstanceOf(NutritionInfo);
    expect(meal.isAnalyzed).toBe(false);
  });

  it('should create meal with provided values', () => {
    const foodItem = new FoodItem({ name: 'Apple', nutrition: new NutritionInfo({ calories: 95 }) });
    const meal = new Meal({
      name: 'Breakfast',
      type: 'breakfast',
      foodItems: [foodItem],
    });
    
    expect(meal.name).toBe('Breakfast');
    expect(meal.type).toBe('breakfast');
    expect(meal.foodItems).toHaveLength(1);
    expect(meal.foodItems[0].name).toBe('Apple');
  });

  it('should add food item and recalculate nutrition', () => {
    const meal = new Meal();
    const foodItem = new FoodItem({ 
      name: 'Apple', 
      nutrition: new NutritionInfo({ calories: 95, protein: 0.5 }) 
    });
    
    meal.addFoodItem(foodItem);
    
    expect(meal.foodItems).toHaveLength(1);
    expect(meal.totalNutrition.calories).toBe(95);
    expect(meal.totalNutrition.protein).toBe(0.5);
  });

  it('should remove food item and recalculate nutrition', () => {
    const foodItem1 = new FoodItem({ 
      name: 'Apple', 
      nutrition: new NutritionInfo({ calories: 95 }) 
    });
    const foodItem2 = new FoodItem({ 
      name: 'Banana', 
      nutrition: new NutritionInfo({ calories: 105 }) 
    });
    
    const meal = new Meal({ foodItems: [foodItem1, foodItem2] });
    meal.calculateTotalNutrition();
    
    expect(meal.totalNutrition.calories).toBe(200);
    
    meal.removeFoodItem(foodItem1.id);
    
    expect(meal.foodItems).toHaveLength(1);
    expect(meal.totalNutrition.calories).toBe(105);
  });

  it('should check if meal is from today', () => {
    const todayMeal = new Meal({ timestamp: new Date() });
    const yesterdayMeal = new Meal({ 
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000) 
    });
    
    expect(todayMeal.isToday()).toBe(true);
    expect(yesterdayMeal.isToday()).toBe(false);
  });

  it('should get date string in YYYY-MM-DD format', () => {
    const date = new Date('2023-12-25T10:30:00Z');
    const meal = new Meal({ timestamp: date });
    
    expect(meal.getDateString()).toBe('2023-12-25');
  });

  it('should convert to and from object', () => {
    const foodItem = new FoodItem({ 
      name: 'Apple', 
      nutrition: new NutritionInfo({ calories: 95 }) 
    });
    const meal = new Meal({
      name: 'Breakfast',
      type: 'breakfast',
      foodItems: [foodItem],
      timestamp: new Date('2023-12-25T08:00:00Z'),
    });
    
    const obj = meal.toObject();
    const restored = Meal.fromObject(obj);
    
    expect(restored.name).toBe(meal.name);
    expect(restored.type).toBe(meal.type);
    expect(restored.foodItems).toHaveLength(1);
    expect(restored.foodItems[0].name).toBe('Apple');
    expect(restored.timestamp.toISOString()).toBe(meal.timestamp.toISOString());
  });
});

describe('UserProfile', () => {
  it('should create user profile with default values', () => {
    const profile = new UserProfile();
    
    expect(profile.id).toBe('default');
    expect(profile.name).toBe('');
    expect(profile.dailyCalorieGoal).toBe(2000);
    expect(profile.createdAt).toBeInstanceOf(Date);
    expect(profile.updatedAt).toBeInstanceOf(Date);
  });

  it('should create user profile with provided values', () => {
    const profile = new UserProfile({
      name: 'John Doe',
      age: 30,
      height: 180,
      weight: 75,
      gender: 'male',
    });
    
    expect(profile.name).toBe('John Doe');
    expect(profile.age).toBe(30);
    expect(profile.height).toBe(180);
    expect(profile.weight).toBe(75);
    expect(profile.gender).toBe('male');
  });

  it('should calculate BMI correctly', () => {
    const profile = new UserProfile({
      height: 180, // cm
      weight: 75,  // kg
    });
    
    const bmi = profile.calculateBMI();
    expect(bmi).toBe(23.1); // 75 / (1.8 * 1.8) = 23.148... rounded to 23.1
  });

  it('should return null BMI when height or weight missing', () => {
    const profile1 = new UserProfile({ height: 180 });
    const profile2 = new UserProfile({ weight: 75 });
    
    expect(profile1.calculateBMI()).toBe(null);
    expect(profile2.calculateBMI()).toBe(null);
  });

  it('should get BMI category correctly', () => {
    const underweight = new UserProfile({ height: 180, weight: 55 });
    const normal = new UserProfile({ height: 180, weight: 70 });
    const overweight = new UserProfile({ height: 180, weight: 85 });
    const obese = new UserProfile({ height: 180, weight: 100 });
    
    expect(underweight.getBMICategory()).toBe('Underweight');
    expect(normal.getBMICategory()).toBe('Normal weight');
    expect(overweight.getBMICategory()).toBe('Overweight');
    expect(obese.getBMICategory()).toBe('Obese');
  });

  it('should update profile and timestamp', () => {
    const profile = new UserProfile({ name: 'John' });
    const originalUpdatedAt = profile.updatedAt;
    
    // Wait a bit to ensure timestamp difference
    setTimeout(() => {
      profile.update({ name: 'John Doe', age: 30 });
      
      expect(profile.name).toBe('John Doe');
      expect(profile.age).toBe(30);
      expect(profile.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    }, 10);
  });

  it('should convert to and from object', () => {
    const profile = new UserProfile({
      name: 'John Doe',
      age: 30,
      height: 180,
      weight: 75,
      gender: 'male',
    });
    
    const obj = profile.toObject();
    const restored = UserProfile.fromObject(obj);
    
    expect(restored.name).toBe(profile.name);
    expect(restored.age).toBe(profile.age);
    expect(restored.height).toBe(profile.height);
    expect(restored.weight).toBe(profile.weight);
    expect(restored.gender).toBe(profile.gender);
  });
});
